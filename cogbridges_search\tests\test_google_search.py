"""
CogBridges Search - Google搜索服务单元测试
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.google_search import GoogleSearchService
from models.search_models import SearchQuery, SearchResult, GoogleSearchResult
from config import config


class TestGoogleSearchService(unittest.TestCase):
    """Google搜索服务测试类"""
    
    def setUp(self):
        """测试前准备"""
        # HTTP模式不需要特殊配置
        pass

    def tearDown(self):
        """测试后清理"""
        pass
    
    def test_service_initialization(self):
        """测试服务初始化"""
        service = GoogleSearchService()

        self.assertIsNotNone(service.search_url)
        self.assertIsNotNone(service.headers)
        self.assertEqual(service.request_count, 0)
        self.assertEqual(service.total_search_time, 0.0)

    def test_initialization_always_works(self):
        """测试HTTP模式初始化总是成功"""
        # HTTP模式不需要API配置，应该总是成功
        service = GoogleSearchService()
        self.assertIsNotNone(service)
    
    @patch('services.google_search.get_proxy_session')
    def test_search_success(self, mock_get_session):
        """测试成功搜索"""
        # 模拟HTML响应
        mock_html = '''
        <html>
        <body>
        <div class="g">
            <h3>Test Post 1</h3>
            <a href="https://reddit.com/r/test/comments/123/test_post_1/">
                <h3>Test Post 1</h3>
            </a>
            <div class="VwiC3b">This is a test post about programming</div>
            <cite>reddit.com</cite>
        </div>
        <div class="g">
            <h3>Test Post 2</h3>
            <a href="https://reddit.com/r/python/comments/456/test_post_2/">
                <h3>Test Post 2</h3>
            </a>
            <div class="VwiC3b">Another test post about Python</div>
            <cite>reddit.com</cite>
        </div>
        </body>
        </html>
        '''

        mock_response = Mock()
        mock_response.text = mock_html
        mock_response.raise_for_status.return_value = None

        mock_session = Mock()
        mock_session.get.return_value = mock_response
        mock_session.headers = {}
        mock_get_session.return_value = mock_session

        service = GoogleSearchService()
        result = service.search("python programming", max_results=2)

        self.assertTrue(result.success)
        self.assertGreaterEqual(len(result.results), 1)  # 至少找到一个结果
        if len(result.results) > 0:
            self.assertTrue(result.results[0].is_reddit_url)
    
    @patch('services.google_search.get_proxy_session')
    def test_search_reddit_posts(self, mock_get_session):
        """测试Reddit帖子搜索"""
        mock_html = '''
        <html>
        <body>
        <div class="g">
            <a href="https://reddit.com/r/python/comments/789/python_tips/">
                <h3>Python Tips</h3>
            </a>
            <div class="VwiC3b">Great Python programming tips</div>
            <cite>reddit.com</cite>
        </div>
        </body>
        </html>
        '''

        mock_response = Mock()
        mock_response.text = mock_html
        mock_response.raise_for_status.return_value = None

        mock_session = Mock()
        mock_session.get.return_value = mock_response
        mock_session.headers = {}
        mock_get_session.return_value = mock_session

        service = GoogleSearchService()
        result = service.search_reddit_posts("python tips", subreddit="python")

        self.assertTrue(result.success)
        # 验证调用了正确的搜索参数
        mock_session.get.assert_called_once()
    
    @patch('services.google_search.get_proxy_session')
    def test_search_http_error(self, mock_get_session):
        """测试HTTP错误处理"""
        import requests

        mock_session = Mock()
        mock_session.get.side_effect = requests.RequestException("Network error")
        mock_session.headers = {}
        mock_get_session.return_value = mock_session

        service = GoogleSearchService()
        result = service.search("test query")

        self.assertFalse(result.success)
        self.assertIn("HTTP请求错误", result.error_message)
    
    def test_parse_search_results(self):
        """测试搜索结果解析"""
        # 创建服务实例（需要模拟配置）
        with patch('services.google_search.build'):
            service = GoogleSearchService()
        
        api_result = {
            'items': [
                {
                    'title': 'Test Title',
                    'link': 'https://example.com/test',
                    'snippet': 'Test snippet',
                    'displayLink': 'example.com'
                },
                {
                    'title': 'Another Title',
                    'link': 'https://reddit.com/r/test/comments/123/',
                    'snippet': 'Reddit post snippet',
                    'displayLink': 'reddit.com'
                }
            ]
        }
        
        results = service._parse_search_results(api_result)
        
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0].title, "Test Title")
        self.assertEqual(results[0].rank, 1)
        self.assertEqual(results[1].title, "Another Title")
        self.assertEqual(results[1].rank, 2)
        self.assertTrue(results[1].is_reddit_url)
    
    def test_parse_search_results_empty(self):
        """测试空搜索结果解析"""
        with patch('services.google_search.build'):
            service = GoogleSearchService()
        
        api_result = {'items': []}
        results = service._parse_search_results(api_result)
        
        self.assertEqual(len(results), 0)
    
    def test_parse_search_results_malformed(self):
        """测试格式错误的搜索结果"""
        with patch('services.google_search.build'):
            service = GoogleSearchService()
        
        api_result = {
            'items': [
                {
                    'title': 'Valid Title',
                    'link': 'https://example.com',
                    'snippet': 'Valid snippet',
                    'displayLink': 'example.com'
                },
                {
                    # 缺少必需字段
                    'title': 'Invalid Item'
                }
            ]
        }
        
        results = service._parse_search_results(api_result)
        
        # 应该只解析出有效的结果
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].title, "Valid Title")
    
    @patch('services.google_search.get_proxy_session')
    def test_get_search_suggestions(self, mock_get_session):
        """测试搜索建议获取"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = ["query", ["suggestion1", "suggestion2", "suggestion3"]]
        
        mock_session = Mock()
        mock_session.get.return_value = mock_response
        mock_get_session.return_value = mock_session
        
        with patch('services.google_search.build'):
            service = GoogleSearchService()
        
        suggestions = service.get_search_suggestions("test query")
        
        self.assertEqual(len(suggestions), 3)
        self.assertIn("suggestion1", suggestions)
        self.assertIn("suggestion2", suggestions)
        self.assertIn("suggestion3", suggestions)
    
    @patch('services.google_search.get_proxy_session')
    def test_get_search_suggestions_error(self, mock_get_session):
        """测试搜索建议获取错误"""
        mock_session = Mock()
        mock_session.get.side_effect = Exception("Network error")
        mock_get_session.return_value = mock_session
        
        with patch('services.google_search.build'):
            service = GoogleSearchService()
        
        suggestions = service.get_search_suggestions("test query")
        
        self.assertEqual(len(suggestions), 0)
    
    @patch('services.google_search.build')
    def test_test_api_connection(self, mock_build):
        """测试API连接测试"""
        mock_api_response = {
            'searchInformation': {'totalResults': '1'},
            'items': [
                {
                    'title': 'Test',
                    'link': 'https://example.com',
                    'snippet': 'Test',
                    'displayLink': 'example.com'
                }
            ]
        }
        
        mock_service = Mock()
        mock_cse = Mock()
        mock_list = Mock()
        
        mock_service.cse.return_value = mock_cse
        mock_cse.list.return_value = mock_list
        mock_list.execute.return_value = mock_api_response
        mock_build.return_value = mock_service
        
        service = GoogleSearchService()
        result = service.test_api_connection()
        
        self.assertTrue(result)
    
    @patch('services.google_search.build')
    def test_get_statistics(self, mock_build):
        """测试统计信息获取"""
        mock_build.return_value = Mock()
        
        service = GoogleSearchService()
        service.request_count = 5
        service.total_search_time = 10.0
        
        stats = service.get_statistics()
        
        self.assertEqual(stats['request_count'], 5)
        self.assertEqual(stats['total_search_time'], 10.0)
        self.assertEqual(stats['average_search_time'], 2.0)
        self.assertTrue(stats['api_configured'])


if __name__ == '__main__':
    unittest.main()
