"""
CogBridges Search - Google搜索服务单元测试
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.google_search import GoogleSearchService
from models.search_models import SearchQuery, SearchResult, GoogleSearchResult
from config import config


class TestGoogleSearchService(unittest.TestCase):
    """Google搜索服务测试类"""
    
    def setUp(self):
        """测试前准备"""
        # HTTP模式不需要特殊配置
        pass

    def tearDown(self):
        """测试后清理"""
        pass
    
    def test_service_initialization(self):
        """测试服务初始化"""
        service = GoogleSearchService()

        self.assertIsNotNone(service.search_url)
        self.assertIsNotNone(service.headers)
        self.assertEqual(service.request_count, 0)
        self.assertEqual(service.total_search_time, 0.0)

    def test_initialization_always_works(self):
        """测试HTTP模式初始化总是成功"""
        # HTTP模式不需要API配置，应该总是成功
        service = GoogleSearchService()
        self.assertIsNotNone(service)
    
    @patch('services.google_search.get_proxy_session')
    def test_search_success(self, mock_get_session):
        """测试成功搜索"""
        # 模拟HTML响应
        mock_html = '''
        <html>
        <body>
        <div class="g">
            <h3>Test Post 1</h3>
            <a href="https://reddit.com/r/test/comments/123/test_post_1/">
                <h3>Test Post 1</h3>
            </a>
            <div class="VwiC3b">This is a test post about programming</div>
            <cite>reddit.com</cite>
        </div>
        <div class="g">
            <h3>Test Post 2</h3>
            <a href="https://reddit.com/r/python/comments/456/test_post_2/">
                <h3>Test Post 2</h3>
            </a>
            <div class="VwiC3b">Another test post about Python</div>
            <cite>reddit.com</cite>
        </div>
        </body>
        </html>
        '''

        mock_response = Mock()
        mock_response.text = mock_html
        mock_response.raise_for_status.return_value = None

        mock_session = Mock()
        mock_session.get.return_value = mock_response
        mock_session.headers = {}
        mock_get_session.return_value = mock_session

        service = GoogleSearchService()
        result = service.search("python programming", max_results=2)

        self.assertTrue(result.success)
        self.assertGreaterEqual(len(result.results), 1)  # 至少找到一个结果
        if len(result.results) > 0:
            self.assertTrue(result.results[0].is_reddit_url)
    
    @patch('services.google_search.get_proxy_session')
    def test_search_reddit_posts(self, mock_get_session):
        """测试Reddit帖子搜索"""
        mock_html = '''
        <html>
        <body>
        <div class="g">
            <a href="https://reddit.com/r/python/comments/789/python_tips/">
                <h3>Python Tips</h3>
            </a>
            <div class="VwiC3b">Great Python programming tips</div>
            <cite>reddit.com</cite>
        </div>
        </body>
        </html>
        '''

        mock_response = Mock()
        mock_response.text = mock_html
        mock_response.raise_for_status.return_value = None

        mock_session = Mock()
        mock_session.get.return_value = mock_response
        mock_session.headers = {}
        mock_get_session.return_value = mock_session

        service = GoogleSearchService()
        result = service.search_reddit_posts("python tips", subreddit="python")

        self.assertTrue(result.success)
        # 验证调用了正确的搜索参数
        mock_session.get.assert_called_once()
    
    @patch('services.google_search.get_proxy_session')
    def test_search_http_error(self, mock_get_session):
        """测试HTTP错误处理"""
        import requests

        mock_session = Mock()
        mock_session.get.side_effect = requests.RequestException("Network error")
        mock_session.headers = {}
        mock_get_session.return_value = mock_session

        service = GoogleSearchService()
        result = service.search("test query")

        self.assertFalse(result.success)
        self.assertIn("HTTP请求错误", result.error_message)
    
    def test_parse_search_results(self):
        """测试搜索结果解析"""
        # HTTP模式使用HTML解析，不是API结果解析
        service = GoogleSearchService()

        # 测试HTML解析功能
        html_content = '''
        <html>
        <body>
        <div class="g">
            <a href="https://example.com/test">
                <h3>Test Title</h3>
            </a>
            <div class="VwiC3b">Test snippet</div>
            <cite>example.com</cite>
        </div>
        <div class="g">
            <a href="https://reddit.com/r/test/comments/123/">
                <h3>Another Title</h3>
            </a>
            <div class="VwiC3b">Reddit post snippet</div>
            <cite>reddit.com</cite>
        </div>
        </body>
        </html>
        '''

        results = service._parse_html_results(html_content, max_results=10)

        self.assertGreaterEqual(len(results), 1)
        # 验证至少有一个结果被解析
        if len(results) > 0:
            self.assertIsNotNone(results[0].title)
            self.assertIsNotNone(results[0].url)
    
    def test_parse_search_results_empty(self):
        """测试空搜索结果解析"""
        service = GoogleSearchService()

        # 测试空HTML内容
        empty_html = '<html><body></body></html>'
        results = service._parse_html_results(empty_html, max_results=10)

        self.assertEqual(len(results), 0)

    def test_parse_search_results_malformed(self):
        """测试格式错误的搜索结果"""
        service = GoogleSearchService()

        # 测试格式不完整的HTML
        malformed_html = '''
        <html>
        <body>
        <div class="g">
            <a href="https://example.com">
                <h3>Valid Title</h3>
            </a>
            <div class="VwiC3b">Valid snippet</div>
            <cite>example.com</cite>
        </div>
        <div class="g">
            <!-- 缺少链接的项目 -->
            <h3>Invalid Item</h3>
            <div class="VwiC3b">No link</div>
        </div>
        </body>
        </html>
        '''

        results = service._parse_html_results(malformed_html, max_results=10)

        # 应该至少解析出一个有效结果
        self.assertGreaterEqual(len(results), 1)
    
    @patch('services.google_search.get_proxy_session')
    def test_get_search_suggestions(self, mock_get_session):
        """测试搜索建议获取"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = ["query", ["suggestion1", "suggestion2", "suggestion3"]]

        mock_session = Mock()
        mock_session.get.return_value = mock_response
        mock_get_session.return_value = mock_session

        service = GoogleSearchService()
        suggestions = service.get_search_suggestions("test query")

        self.assertEqual(len(suggestions), 3)
        self.assertIn("suggestion1", suggestions)
        self.assertIn("suggestion2", suggestions)
        self.assertIn("suggestion3", suggestions)

    @patch('services.google_search.get_proxy_session')
    def test_get_search_suggestions_error(self, mock_get_session):
        """测试搜索建议获取错误"""
        mock_session = Mock()
        mock_session.get.side_effect = Exception("Network error")
        mock_get_session.return_value = mock_session

        service = GoogleSearchService()
        suggestions = service.get_search_suggestions("test query")

        self.assertEqual(len(suggestions), 0)
    
    @patch('services.google_search.get_proxy_session')
    def test_test_api_connection(self, mock_get_session):
        """测试连接测试"""
        # 模拟成功的HTTP响应，包含搜索结果
        mock_response = Mock()
        mock_response.text = '''
        <html>
        <body>
        <div class="g">
            <a href="https://example.com/test">
                <h3>Test Result</h3>
            </a>
            <div class="VwiC3b">Test snippet</div>
            <cite>example.com</cite>
        </div>
        </body>
        </html>
        '''
        mock_response.raise_for_status.return_value = None

        mock_session = Mock()
        mock_session.get.return_value = mock_response
        mock_session.headers = {}
        mock_get_session.return_value = mock_session

        service = GoogleSearchService()
        result = service.test_connection()

        self.assertTrue(result)

    def test_get_statistics(self):
        """测试统计信息获取"""
        service = GoogleSearchService()
        service.request_count = 5
        service.total_search_time = 10.0

        stats = service.get_statistics()

        self.assertEqual(stats['request_count'], 5)
        self.assertEqual(stats['total_search_time'], 10.0)
        self.assertEqual(stats['average_search_time'], 2.0)
        self.assertEqual(stats['search_method'], 'HTTP')


if __name__ == '__main__':
    unittest.main()
