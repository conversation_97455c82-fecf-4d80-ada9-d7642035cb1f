"""
CogBridges Search - Google搜索服务
使用Google Custom Search API实现搜索，支持Reddit内容搜索
"""

import time
import re
from typing import List, Optional, Dict, Any
from urllib.parse import quote_plus
import requests
from tenacity import retry, stop_after_attempt, wait_exponential

from config import config
from models.search_models import SearchQuery, SearchResult, GoogleSearchResult
from utils.logger_utils import get_logger, log_api_call, log_search_operation
from utils.proxy_utils import get_proxy_session


class GoogleSearchService:
    """Google搜索服务类 - 使用Google Custom Search API"""

    def __init__(self):
        """初始化Google搜索服务"""
        self.logger = get_logger(__name__)

        # 验证API配置
        if not config.google_search_configured:
            self.logger.error("Google Custom Search API未配置")
            raise ValueError("Google Custom Search API配置不完整")

        # Google Custom Search API配置
        self.api_key = config.GOOGLE_SEARCH_API_KEY
        self.engine_id = config.GOOGLE_SEARCH_ENGINE_ID
        self.api_url = "https://www.googleapis.com/customsearch/v1"

        # 请求统计
        self.request_count = 0
        self.total_search_time = 0.0

        self.logger.info("Google搜索服务初始化成功（Custom Search API模式）")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    @log_api_call("Google", "search", "GET")
    def search(
        self,
        query: str,
        max_results: int = None,
        site_filter: str = "site:reddit.com",
        **kwargs
    ) -> SearchResult:
        """
        执行Google搜索

        Args:
            query: 搜索查询
            max_results: 最大结果数量
            site_filter: 站点过滤器
            **kwargs: 其他搜索参数

        Returns:
            搜索结果对象
        """
        start_time = time.time()

        # 创建搜索查询对象
        search_query = SearchQuery(
            query=query,
            max_results=max_results or config.GOOGLE_SEARCH_RESULTS_COUNT,
            site_filter=site_filter
        )

        self.logger.info(f"开始Google搜索: {query}")

        try:
            # 构建搜索查询字符串
            if site_filter:
                full_query = f"{query} {site_filter}"
            else:
                full_query = query

            # 构建搜索参数
            search_params = {
                'q': full_query,
                'num': min(search_query.max_results, 10),  # Google限制每页最多10个结果
                'start': kwargs.get('start', 0),
                'hl': 'en',  # 界面语言
                'lr': 'lang_en',  # 搜索语言
                'safe': 'medium',
                'filter': '0'  # 不过滤相似结果
            }

            self.logger.debug(f"搜索参数: {search_params}")

            # 获取代理会话
            session = get_proxy_session()
            session.headers.update(self.headers)

            # 发送搜索请求
            response = session.get(self.search_url, params=search_params, timeout=30)
            response.raise_for_status()

            # 解析搜索结果
            search_results = self._parse_html_results(response.text, search_query.max_results)

            # 计算搜索时间
            search_time = time.time() - start_time

            # 创建搜索结果对象
            search_result = SearchResult(
                query=search_query,
                results=search_results,
                total_results=len(search_results),  # HTML解析无法获取准确总数
                search_time=search_time,
                success=True
            )

            # 更新统计信息
            self.request_count += 1
            self.total_search_time += search_time

            # 记录搜索操作
            log_search_operation(query, len(search_results), search_time)

            self.logger.info(f"搜索完成: 找到 {len(search_results)} 个结果，耗时 {search_time:.2f}秒")

            return search_result

        except requests.RequestException as e:
            error_msg = f"HTTP请求错误: {e}"
            self.logger.error(error_msg)

            return SearchResult(
                query=search_query,
                success=False,
                error_message=error_msg,
                search_time=time.time() - start_time
            )

        except Exception as e:
            error_msg = f"搜索异常: {e}"
            self.logger.error(error_msg)

            return SearchResult(
                query=search_query,
                success=False,
                error_message=error_msg,
                search_time=time.time() - start_time
            )
    
    def _parse_html_results(self, html_content: str, max_results: int) -> List[GoogleSearchResult]:
        """
        解析Google搜索HTML页面结果

        Args:
            html_content: Google搜索页面HTML内容
            max_results: 最大结果数量

        Returns:
            解析后的搜索结果列表
        """
        results = []

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 查找搜索结果容器
            # Google搜索结果的CSS选择器可能会变化，这里使用多种选择器
            result_selectors = [
                'div.g',  # 标准搜索结果
                'div[data-ved]',  # 带有data-ved属性的结果
                '.rc',  # 旧版选择器
            ]

            search_results = []
            for selector in result_selectors:
                search_results = soup.select(selector)
                if search_results:
                    break

            if not search_results:
                self.logger.warning("未找到搜索结果容器")
                return results

            for i, result_div in enumerate(search_results[:max_results]):
                try:
                    # 提取标题和链接
                    title_element = result_div.select_one('h3')
                    link_element = result_div.select_one('a[href]')

                    if not title_element or not link_element:
                        continue

                    title = title_element.get_text(strip=True)
                    url = link_element.get('href', '')

                    # 清理URL（移除Google重定向）
                    if url.startswith('/url?q='):
                        import urllib.parse
                        parsed = urllib.parse.parse_qs(urllib.parse.urlparse(url).query)
                        url = parsed.get('q', [''])[0]
                    elif url.startswith('/search?'):
                        continue  # 跳过内部搜索链接

                    # 提取摘要
                    snippet_selectors = [
                        '.VwiC3b',  # 新版摘要选择器
                        '.s',       # 旧版摘要选择器
                        '.st',      # 另一个旧版选择器
                    ]

                    snippet = ""
                    for selector in snippet_selectors:
                        snippet_element = result_div.select_one(selector)
                        if snippet_element:
                            snippet = snippet_element.get_text(strip=True)
                            break

                    # 提取显示URL
                    display_url_element = result_div.select_one('cite')
                    display_url = display_url_element.get_text(strip=True) if display_url_element else ""

                    # 验证URL有效性
                    if not url or not url.startswith(('http://', 'https://')):
                        continue

                    # 创建搜索结果对象
                    result = GoogleSearchResult(
                        title=title,
                        url=url,
                        snippet=snippet,
                        display_url=display_url,
                        rank=i + 1
                    )

                    results.append(result)

                except Exception as e:
                    self.logger.warning(f"解析搜索结果项失败: {e}")
                    continue

            self.logger.debug(f"成功解析 {len(results)} 个搜索结果")

        except Exception as e:
            self.logger.error(f"解析HTML搜索结果失败: {e}")

        return results
    
    def search_reddit_posts(
        self,
        query: str,
        max_results: int = None,
        subreddit: str = None,
        time_filter: str = "m6"
    ) -> SearchResult:
        """
        专门搜索Reddit帖子
        
        Args:
            query: 搜索查询
            max_results: 最大结果数量
            subreddit: 指定子版块
            time_filter: 时间过滤器
            
        Returns:
            搜索结果对象
        """
        # 构建Reddit特定的搜索查询
        if subreddit:
            site_filter = f"site:reddit.com/r/{subreddit}"
        else:
            site_filter = "site:reddit.com"
        
        # 添加Reddit帖子特定的关键词
        reddit_query = f"{query} inurl:comments"
        
        return self.search(
            query=reddit_query,
            max_results=max_results,
            site_filter=site_filter,
            dateRestrict=time_filter
        )
    
    def get_search_suggestions(self, query: str) -> List[str]:
        """
        获取搜索建议（使用Google Suggest API）
        
        Args:
            query: 搜索查询
            
        Returns:
            搜索建议列表
        """
        try:
            session = get_proxy_session()
            
            # Google Suggest API
            suggest_url = "http://suggestqueries.google.com/complete/search"
            params = {
                'client': 'firefox',
                'q': query,
                'hl': 'en'
            }
            
            response = session.get(suggest_url, params=params, timeout=5)
            
            if response.status_code == 200:
                # 解析JSON响应
                suggestions_data = response.json()
                if len(suggestions_data) > 1:
                    return suggestions_data[1][:5]  # 返回前5个建议
            
            return []
            
        except Exception as e:
            self.logger.warning(f"获取搜索建议失败: {e}")
            return []
    
    def test_connection(self) -> bool:
        """
        测试Google搜索连接

        Returns:
            连接是否成功
        """
        try:
            # 执行简单的测试搜索
            test_result = self.search("test", max_results=1)

            if test_result.success and len(test_result.results) > 0:
                self.logger.info("Google搜索连接测试成功")
                return True
            else:
                self.logger.error(f"Google搜索连接测试失败: {test_result.error_message}")
                return False

        except Exception as e:
            self.logger.error(f"Google搜索连接测试异常: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取服务统计信息

        Returns:
            统计信息字典
        """
        avg_search_time = (
            self.total_search_time / self.request_count
            if self.request_count > 0 else 0
        )

        return {
            "request_count": self.request_count,
            "total_search_time": self.total_search_time,
            "average_search_time": avg_search_time,
            "search_method": "HTTP",
            "max_results_per_search": config.GOOGLE_SEARCH_RESULTS_COUNT
        }


if __name__ == "__main__":
    # 测试Google搜索服务
    print("CogBridges Search - Google搜索服务测试")
    print("=" * 50)

    try:
        # 创建搜索服务
        search_service = GoogleSearchService()

        # 测试连接
        print("🔗 测试搜索连接...")
        if search_service.test_connection():
            print("✅ 搜索连接正常")
        else:
            print("❌ 搜索连接失败")
            exit(1)

        # 执行测试搜索
        print("\n🔍 执行测试搜索...")
        test_query = "python programming"
        result = search_service.search_reddit_posts(test_query, max_results=3)

        if result.success:
            print(f"✅ 搜索成功: 找到 {len(result.results)} 个结果")

            for i, item in enumerate(result.results, 1):
                print(f"\n{i}. {item.title}")
                print(f"   URL: {item.url}")
                print(f"   摘要: {item.snippet[:100]}...")
                print(f"   Reddit URL: {item.is_reddit_url}")
        else:
            print(f"❌ 搜索失败: {result.error_message}")

        # 显示统计信息
        print("\n📊 服务统计:")
        stats = search_service.get_statistics()
        for key, value in stats.items():
            print(f"  {key}: {value}")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
