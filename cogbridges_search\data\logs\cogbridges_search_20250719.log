2025-07-19 14:51:20 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 14:51:20 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 14:51:22 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 14:51:22 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 14:51:22 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 14:51:22 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: python programming tips
2025-07-19 14:51:22 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - python programming tips
2025-07-19 14:51:22 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 14:51:22 - services.google_search_api - INFO - 开始Google Custom Search API搜索: python programming tips
2025-07-19 14:51:22 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 14:51:23 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.37秒
2025-07-19 14:51:23 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.37秒
2025-07-19 14:51:23 - services.cogbridges_service - INFO - 步骤1完成: 找到 0 个Reddit帖子, 耗时: 1.37秒
2025-07-19 14:51:23 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: machine learning tutorial
2025-07-19 14:51:23 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - machine learning tutorial
2025-07-19 14:51:23 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 14:51:23 - services.google_search_api - INFO - 开始Google Custom Search API搜索: machine learning tutorial
2025-07-19 14:51:23 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 14:51:26 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 2.45秒
2025-07-19 14:51:26 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 2.45秒
2025-07-19 14:51:26 - services.cogbridges_service - INFO - 步骤1完成: 找到 0 个Reddit帖子, 耗时: 2.45秒
2025-07-19 14:51:26 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: web development best practices
2025-07-19 14:51:26 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - web development best practices
2025-07-19 14:51:26 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 14:51:26 - services.google_search_api - INFO - 开始Google Custom Search API搜索: web development best practices
2025-07-19 14:51:26 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 14:51:28 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.95秒
2025-07-19 14:51:28 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.96秒
2025-07-19 14:51:28 - services.cogbridges_service - INFO - 步骤1完成: 找到 0 个Reddit帖子, 耗时: 1.96秒
2025-07-19 14:52:30 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 14:52:30 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 14:52:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 14:52:30 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 14:52:30 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 14:52:30 - services.cogbridges_service - INFO - 步骤2: 并行获取 3 个Reddit帖子的内容和评论
2025-07-19 14:52:30 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 14:52:30 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 14:52:32 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 14:52:32 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 14:52:32 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 14:52:32 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 14:52:33 - services.reddit_service - ERROR - 获取Reddit帖子失败: received 404 HTTP response
2025-07-19 14:52:34 - services.reddit_service - INFO - 获取帖子成功: My boyfriend (M19) suddenly became extremely dista...
2025-07-19 14:52:34 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 14:52:34 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 14:52:35 - services.reddit_service - INFO - 获取评论成功: 3 条评论
2025-07-19 14:52:36 - services.reddit_service - ERROR - 获取Reddit帖子失败: received 404 HTTP response
2025-07-19 14:52:36 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 1 个帖子数据, 耗时: 5.44秒
2025-07-19 14:52:36 - services.cogbridges_service - INFO - 步骤3: 并行获取评论者历史数据
2025-07-19 14:52:36 - services.cogbridges_service - INFO - 需要获取 1 个评论者的历史数据
2025-07-19 14:52:37 - services.reddit_service - INFO - 获取用户 the_genius324 在 relationships 的评论: 0 条
2025-07-19 14:52:38 - services.reddit_service - INFO - 获取用户 the_genius324 在 relationships 的帖子: 0 条
2025-07-19 14:52:38 - services.cogbridges_service - INFO - 步骤3完成: 获取了 1 个用户的历史数据, 耗时: 2.52秒
2025-07-19 14:52:38 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 14:52:38 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 14:52:38 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 14:52:38 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 14:52:38 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 14:52:38 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 14:52:38 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 14:52:41 - services.reddit_service - ERROR - 获取Reddit帖子失败: received 404 HTTP response
2025-07-19 14:57:43 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 14:57:43 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 14:57:43 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 14:57:43 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 14:57:43 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 14:57:43 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: python programming tips
2025-07-19 14:57:43 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - python programming tips
2025-07-19 14:57:43 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 14:57:43 - services.google_search_api - INFO - 开始Google Custom Search API搜索: python programming tips
2025-07-19 14:57:43 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 14:57:44 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.20秒
2025-07-19 14:57:44 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.20秒
2025-07-19 14:57:44 - services.cogbridges_service - INFO - 步骤1完成: 找到 0 个Reddit帖子, 耗时: 1.20秒
2025-07-19 14:57:44 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: machine learning tutorial
2025-07-19 14:57:44 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - machine learning tutorial
2025-07-19 14:57:44 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 14:57:44 - services.google_search_api - INFO - 开始Google Custom Search API搜索: machine learning tutorial
2025-07-19 14:57:44 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 14:57:45 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.32秒
2025-07-19 14:57:45 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.32秒
2025-07-19 14:57:45 - services.cogbridges_service - INFO - 步骤1完成: 找到 0 个Reddit帖子, 耗时: 1.32秒
2025-07-19 15:07:38 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 15:07:38 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:07:38 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 15:07:38 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 15:07:38 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 15:07:38 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: python programming tips
2025-07-19 15:07:38 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - python programming tips
2025-07-19 15:07:38 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:07:38 - services.google_search_api - INFO - 开始Google Custom Search API搜索: python programming tips
2025-07-19 15:07:38 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:07:39 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 0.97秒
2025-07-19 15:07:39 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 0.97秒
2025-07-19 15:07:39 - services.cogbridges_service - INFO - 步骤1完成: 找到 0 个Reddit帖子, 耗时: 0.98秒
2025-07-19 15:07:39 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: machine learning tutorial
2025-07-19 15:07:39 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - machine learning tutorial
2025-07-19 15:07:39 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:07:39 - services.google_search_api - INFO - 开始Google Custom Search API搜索: machine learning tutorial
2025-07-19 15:07:39 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:07:40 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 0.95秒
2025-07-19 15:07:40 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 0.95秒
2025-07-19 15:07:40 - services.cogbridges_service - INFO - 步骤1完成: 找到 0 个Reddit帖子, 耗时: 0.95秒
