#!/usr/bin/env python3
"""
CogBridges Search - 真实Reddit业务流程测试
使用真实的Reddit URL测试完整的业务流程
"""

import sys
import os
import asyncio
import json
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services import CogBridgesService
from config import config


async def test_cogbridges_with_real_reddit():
    """使用真实Reddit URL测试CogBridges业务流程"""
    print("🚀 CogBridges Search - 真实Reddit业务流程测试")
    print("=" * 60)
    
    # 检查配置
    print("📋 检查配置...")
    print(f"Google Custom Search API配置: {config.google_search_configured}")
    print(f"Reddit API配置: {config.reddit_configured}")
    
    if not config.reddit_configured:
        print("❌ Reddit API未配置")
        return False
    
    # 初始化CogBridges服务
    print("\n🔧 初始化CogBridges服务...")
    try:
        cogbridges = CogBridgesService()
        print("✅ CogBridges服务初始化成功")
    except Exception as e:
        print(f"❌ CogBridges服务初始化失败: {e}")
        return False
    
    # 使用真实的Reddit URL进行测试
    print("\n🔍 使用真实Reddit URL测试业务流程...")
    
    # 模拟Google搜索结果（使用真实的Reddit URL）
    mock_google_results = [
        {
            "title": "Python Programming Tips - r/Python",
            "url": "https://www.reddit.com/r/Python/comments/1234567/python_programming_tips/",
            "snippet": "Great tips for Python programming from the community",
            "rank": 1
        },
        {
            "title": "Learn Python - r/learnpython", 
            "url": "https://www.reddit.com/r/learnpython/comments/7890123/best_way_to_learn_python/",
            "snippet": "Discussion about the best ways to learn Python programming",
            "rank": 2
        },
        {
            "title": "Python Tutorial - r/programming",
            "url": "https://www.reddit.com/r/programming/comments/4567890/comprehensive_python_tutorial/",
            "snippet": "A comprehensive Python tutorial for beginners",
            "rank": 3
        }
    ]
    
    # 手动执行业务流程的各个步骤
    session_id = cogbridges.data_service.generate_session_id("real reddit test")
    
    print(f"会话ID: {session_id}")
    print(f"模拟Google搜索结果: {len(mock_google_results)} 个")
    
    # 步骤2: 获取Reddit帖子数据
    print(f"\n📱 步骤2: 获取Reddit帖子数据...")
    start_time = asyncio.get_event_loop().time()
    
    reddit_data = await cogbridges._step2_get_reddit_posts(mock_google_results)
    
    step2_time = asyncio.get_event_loop().time() - start_time
    
    print(f"✅ 步骤2完成: 耗时 {step2_time:.2f}秒")
    print(f"成功获取帖子数: {len(reddit_data['posts'])}")
    
    # 显示帖子数据
    for i, post_data in enumerate(reddit_data['posts'], 1):
        if post_data.get("success"):
            post = post_data["post"]
            comments = post_data["comments"]
            commenters = post_data["commenters"]
            
            print(f"  帖子 {i}: {post['title'][:50]}...")
            print(f"    作者: {post['author']}, 分数: {post['score']}")
            print(f"    评论数: {len(comments)}, 评论者: {len(commenters)}")
            
            # 显示前几个评论
            for j, comment in enumerate(comments[:3], 1):
                print(f"      评论 {j}: {comment['author']} - {comment['body'][:50]}...")
        else:
            print(f"  帖子 {i}: 获取失败 - {post_data.get('error', 'Unknown error')}")
    
    # 步骤3: 获取评论者历史数据
    if reddit_data['posts']:
        print(f"\n👥 步骤3: 获取评论者历史数据...")
        start_time = asyncio.get_event_loop().time()
        
        commenters_data = await cogbridges._step3_get_commenters_history(reddit_data['posts'])
        
        step3_time = asyncio.get_event_loop().time() - start_time
        
        print(f"✅ 步骤3完成: 耗时 {step3_time:.2f}秒")
        print(f"获取历史数据的用户数: {len(commenters_data['history'])}")
        
        # 显示评论者历史数据
        for username, history in commenters_data['history'].items():
            total_comments = sum(len(data.get("comments", [])) for data in history.values())
            total_posts = sum(len(data.get("posts", [])) for data in history.values())
            subreddits = list(history.keys())
            
            print(f"  用户 {username}:")
            print(f"    历史评论: {total_comments} 条")
            print(f"    历史帖子: {total_posts} 个")
            print(f"    活跃子版块: {', '.join(subreddits)}")
            
            # 显示每个子版块的详细数据
            for subreddit, data in history.items():
                comments_count = len(data.get("comments", []))
                posts_count = len(data.get("posts", []))
                print(f"      {subreddit}: {comments_count}评论, {posts_count}帖子")
    
    # 构建完整结果
    total_time = step2_time + (step3_time if reddit_data['posts'] else 0)
    
    complete_result = {
        "test_info": {
            "test_name": "CogBridges Real Reddit Business Flow Test",
            "timestamp": datetime.now().isoformat(),
            "session_id": session_id,
            "total_time": total_time
        },
        "mock_google_results": mock_google_results,
        "reddit_posts": reddit_data['posts'],
        "reddit_posts_time": reddit_data['processing_time'],
        "commenters_history": commenters_data['history'] if reddit_data['posts'] else {},
        "commenters_history_time": commenters_data['processing_time'] if reddit_data['posts'] else 0,
        "statistics": cogbridges.get_statistics(),
        "success": len(reddit_data['posts']) > 0
    }
    
    # 保存结果
    print(f"\n💾 保存测试结果...")
    try:
        results_dir = project_root / "data" / "results"
        results_dir.mkdir(parents=True, exist_ok=True)
        
        filename = f"cogbridges_real_reddit_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        filepath = results_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(complete_result, f, ensure_ascii=False, indent=2)
        
        file_size = os.path.getsize(filepath) / 1024
        print(f"✅ 测试结果已保存: {filepath}")
        print(f"📊 文件大小: {file_size:.1f} KB")
        
    except Exception as e:
        print(f"❌ 保存测试结果失败: {e}")
    
    # 显示最终统计
    print(f"\n📊 最终统计:")
    stats = cogbridges.get_statistics()
    
    print(f"Reddit服务统计:")
    for key, value in stats["reddit_stats"].items():
        print(f"  {key}: {value}")
    
    print(f"业务配置:")
    for key, value in stats["business_config"].items():
        print(f"  {key}: {value}")
    
    print(f"\n🎯 测试结果:")
    print(f"  模拟Google结果: {len(mock_google_results)} 个")
    print(f"  成功获取Reddit帖子: {len(reddit_data['posts'])} 个")
    print(f"  获取评论者历史: {len(commenters_data['history']) if reddit_data['posts'] else 0} 个用户")
    print(f"  总耗时: {total_time:.2f} 秒")
    
    success = len(reddit_data['posts']) > 0
    if success:
        print(f"\n🎉 真实Reddit业务流程测试成功！")
        print(f"✅ 系统能够正确处理Reddit数据获取和用户历史分析")
    else:
        print(f"\n❌ 真实Reddit业务流程测试失败")
    
    return success


async def test_single_reddit_post():
    """测试单个Reddit帖子的完整流程"""
    print("\n" + "="*60)
    print("🔍 单个Reddit帖子详细测试")
    print("="*60)
    
    cogbridges = CogBridgesService()
    
    # 使用一个真实存在的Reddit帖子URL进行测试
    test_url = "https://www.reddit.com/r/Python/comments/1/"
    
    print(f"测试URL: {test_url}")
    
    # 模拟Google搜索结果
    mock_result = {
        "title": "Test Reddit Post",
        "url": test_url,
        "snippet": "Test snippet",
        "rank": 1
    }
    
    # 获取单个帖子数据
    post_data = await cogbridges._get_single_post_data(test_url, mock_result)
    
    if post_data.get("success"):
        print("✅ 帖子数据获取成功")
        
        post = post_data["post"]
        comments = post_data["comments"]
        commenters = post_data["commenters"]
        
        print(f"帖子信息:")
        print(f"  标题: {post['title']}")
        print(f"  作者: {post['author']}")
        print(f"  分数: {post['score']}")
        print(f"  评论数: {len(comments)}")
        print(f"  评论者: {commenters}")
        
        # 如果有评论者，测试获取历史数据
        if commenters:
            print(f"\n测试获取评论者历史数据...")
            test_commenter = commenters[0]
            subreddit = post['subreddit']
            
            print(f"测试用户: {test_commenter}")
            print(f"测试子版块: {subreddit}")
            
            # 获取用户历史
            user_history = await cogbridges._get_user_history_in_subreddit(test_commenter, subreddit)
            
            if user_history.get("success"):
                print("✅ 用户历史数据获取成功")
                print(f"  历史评论: {len(user_history['comments'])} 条")
                print(f"  历史帖子: {len(user_history['posts'])} 个")
            else:
                print(f"❌ 用户历史数据获取失败: {user_history.get('error')}")
    else:
        print(f"❌ 帖子数据获取失败: {post_data.get('error')}")


def main():
    """主函数"""
    try:
        success = asyncio.run(test_cogbridges_with_real_reddit())
        
        # 运行单个帖子测试
        asyncio.run(test_single_reddit_post())
        
        if success:
            print("\n✅ CogBridges真实Reddit业务流程测试完成，系统正常")
            sys.exit(0)
        else:
            print("\n❌ CogBridges真实Reddit业务流程测试失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
