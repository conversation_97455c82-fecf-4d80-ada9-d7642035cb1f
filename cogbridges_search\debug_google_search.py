#!/usr/bin/env python3
"""
调试Google搜索问题
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.proxy_utils import get_proxy_session
from bs4 import BeautifulSoup


def debug_google_search():
    """调试Google搜索"""
    print("🔍 调试Google搜索...")
    
    try:
        session = get_proxy_session()
        
        # 测试搜索
        search_url = "https://www.google.com/search"
        params = {
            'q': 'python programming',
            'num': 5,
            'hl': 'en'
        }
        
        print(f"请求URL: {search_url}")
        print(f"参数: {params}")
        
        response = session.get(search_url, params=params, timeout=10)
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            # 保存HTML到文件以便检查
            html_file = project_root / "debug_google_response.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"HTML响应已保存到: {html_file}")
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找搜索结果容器
            print("\n🔍 查找搜索结果容器...")
            
            # 尝试不同的选择器
            selectors = [
                'div.g',  # 传统的搜索结果容器
                'div[data-ved]',  # 带有data-ved属性的div
                'div.tF2Cxc',  # 新的搜索结果容器
                'div.MjjYud',  # 另一种可能的容器
                'div.yuRUbf',  # 链接容器
            ]
            
            for selector in selectors:
                results = soup.select(selector)
                print(f"选择器 '{selector}': 找到 {len(results)} 个元素")
                
                if results:
                    for i, result in enumerate(results[:3], 1):
                        print(f"  结果 {i}:")
                        
                        # 查找标题
                        title_selectors = ['h3', 'h3 > span', 'div[role="heading"]']
                        title = None
                        for title_sel in title_selectors:
                            title_elem = result.select_one(title_sel)
                            if title_elem:
                                title = title_elem.get_text(strip=True)
                                break
                        
                        # 查找链接
                        link_selectors = ['a[href]', 'a[data-ved]']
                        link = None
                        for link_sel in link_selectors:
                            link_elem = result.select_one(link_sel)
                            if link_elem and link_elem.get('href'):
                                href = link_elem.get('href')
                                if href.startswith('http'):
                                    link = href
                                    break
                        
                        # 查找摘要
                        snippet_selectors = [
                            'div.VwiC3b', 'span.st', 'div.s', 
                            'div[data-sncf]', 'div.IsZvec'
                        ]
                        snippet = None
                        for snippet_sel in snippet_selectors:
                            snippet_elem = result.select_one(snippet_sel)
                            if snippet_elem:
                                snippet = snippet_elem.get_text(strip=True)
                                break
                        
                        print(f"    标题: {title}")
                        print(f"    链接: {link}")
                        print(f"    摘要: {snippet[:100] if snippet else None}...")
                        print()
            
            # 检查是否被反爬虫机制阻止
            if "Our systems have detected unusual traffic" in response.text:
                print("⚠️ 检测到Google反爬虫机制")
            
            if "blocked" in response.text.lower():
                print("⚠️ 可能被Google阻止")
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应内容: {response.text[:500]}...")
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_google_search()
