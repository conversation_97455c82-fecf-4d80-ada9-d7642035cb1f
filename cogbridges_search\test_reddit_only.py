#!/usr/bin/env python3
"""
CogBridges Search - 仅测试Reddit API
专门测试Reddit API功能，跳过Google搜索
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services import RedditService, DataService
from utils.proxy_utils import get_proxy_info
from config import config


async def test_reddit_api():
    """测试Reddit API功能"""
    print("🚀 CogBridges Search - Reddit API测试")
    print("=" * 60)
    
    # 1. 检查代理配置
    print("🔍 检查代理配置...")
    proxy_info = get_proxy_info()
    print(f"代理已配置: {proxy_info['proxy_configured']}")
    if proxy_info['proxy_configured']:
        print(f"代理设置: {proxy_info['proxy_settings']}")
        
        # 测试代理连接（跳过，直接测试Reddit API）
        print("\n🌐 跳过代理连接测试，直接测试Reddit API...")
        print("⚠️ 如果Reddit API测试失败，请检查Clash代理是否正常运行")
    
    # 2. 检查Reddit配置
    print(f"\n📋 检查Reddit配置...")
    print(f"Reddit配置: {config.reddit_configured}")
    print(f"Client ID: {config.REDDIT_CLIENT_ID}")
    print(f"User Agent: {config.REDDIT_USER_AGENT}")
    
    # 3. 测试Reddit服务
    print(f"\n📱 测试Reddit服务...")
    try:
        reddit_service = RedditService()
        print("✅ Reddit服务初始化成功")
        
        if not reddit_service.configured:
            print("❌ Reddit API未正确配置")
            return False
        
        # 测试连接
        print("🔗 测试Reddit API连接...")
        if reddit_service.test_api_connection():
            print("✅ Reddit API连接正常")
        else:
            print("❌ Reddit API连接失败")
            return False
        
        # 测试URL解析
        print("\n🔍 测试URL解析...")
        test_urls = [
            "https://www.reddit.com/r/Python/comments/abc123/test_post/",
            "https://reddit.com/r/programming/",
            "https://www.reddit.com/user/testuser/",
            "https://invalid-url.com"
        ]
        
        for url in test_urls:
            result = reddit_service.parse_reddit_url(url)
            print(f"  URL: {url}")
            print(f"  解析结果: {result}")
        
        # 测试获取帖子详情（使用一个真实的Reddit帖子）
        print("\n📝 测试获取Reddit帖子详情...")
        # 使用一个比较稳定的测试帖子URL
        test_post_urls = [
            "https://www.reddit.com/r/test/comments/1/",
            "https://www.reddit.com/r/announcements/comments/1/",
        ]
        
        for test_post_url in test_post_urls:
            try:
                print(f"  尝试获取帖子: {test_post_url}")
                post_details = await reddit_service.get_post_details(test_post_url)
                if post_details:
                    print(f"  ✅ 获取帖子成功: {post_details.title[:50]}...")
                    print(f"     作者: {post_details.author}")
                    print(f"     分数: {post_details.score}")
                    print(f"     评论数: {post_details.num_comments}")
                    
                    # 测试获取评论
                    print(f"  💬 测试获取评论...")
                    try:
                        comments = await reddit_service.get_post_comments(test_post_url, limit=3)
                        print(f"     ✅ 获取评论成功: 找到 {len(comments)} 条评论")
                        for i, comment in enumerate(comments[:2], 1):
                            print(f"       {i}. {comment.author}: {comment.body[:50]}...")
                    except Exception as e:
                        print(f"     ⚠️ 获取评论失败: {e}")
                    
                    break  # 成功获取一个帖子就够了
                else:
                    print(f"  ⚠️ 未找到帖子")
            except Exception as e:
                print(f"  ⚠️ 获取帖子失败: {e}")
        
        # 测试提取评论者
        print("\n👥 测试提取评论者...")
        from models.reddit_models import RedditComment
        
        test_comments = [
            RedditComment(
                id="1", body="Test comment 1", author="user1",
                score=10, created_utc=1640995200, parent_id="t3_abc123",
                subreddit="test", permalink="/test/1"
            ),
            RedditComment(
                id="2", body="Test comment 2", author="user2",
                score=5, created_utc=1640995300, parent_id="t3_abc123",
                subreddit="test", permalink="/test/2"
            ),
            RedditComment(
                id="3", body="[deleted]", author="[deleted]",
                score=0, created_utc=1640995400, parent_id="t3_abc123",
                subreddit="test", permalink="/test/3"
            )
        ]
        
        commenters = reddit_service.extract_commenters(test_comments)
        print(f"  提取到的评论者: {commenters}")
        
    except Exception as e:
        print(f"❌ Reddit服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 4. 测试数据服务
    print(f"\n💾 测试数据服务...")
    try:
        data_service = DataService()
        print("✅ 数据服务初始化成功")
        
        # 创建测试数据
        test_reddit_data = {
            "posts_data": [
                {
                    "post": {
                        "id": "test123",
                        "title": "Test Post",
                        "author": "testuser",
                        "score": 100,
                        "subreddit": "test"
                    },
                    "comments": test_comments[:2]
                }
            ],
            "user_histories": {},
            "statistics": {
                "total_posts": 1,
                "total_commenters": 2,
                "processing_time": 1.5
            },
            "success": True
        }
        
        # 测试保存Reddit数据
        session_id = data_service.generate_session_id("reddit test")
        filepath = data_service.save_reddit_data(test_reddit_data, session_id)
        
        if os.path.exists(filepath):
            print(f"✅ Reddit数据保存成功: {filepath}")
        else:
            print("❌ Reddit数据保存失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据服务测试失败: {e}")
        return False
    
    # 5. 显示统计信息
    print(f"\n📊 服务统计信息:")
    
    reddit_stats = reddit_service.get_statistics()
    print(f"Reddit服务统计:")
    for key, value in reddit_stats.items():
        print(f"  {key}: {value}")
    
    print(f"\n🎉 Reddit API测试完成，所有功能正常！")
    return True


def main():
    """主函数"""
    try:
        success = asyncio.run(test_reddit_api())
        if success:
            print("\n✅ Reddit API测试完成，所有功能正常")
            sys.exit(0)
        else:
            print("\n❌ Reddit API测试失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
