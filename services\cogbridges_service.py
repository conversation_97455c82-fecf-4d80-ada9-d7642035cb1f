"""
CogBridges Search - 核心业务流程服务
实现完整的串行业务流程：Google搜索 -> Reddit帖子获取 -> 评论者历史数据获取
"""

import asyncio
import time
from typing import List, Dict, Any, Optional
from datetime import datetime
from dataclasses import dataclass, field

from config import config
from services.google_search_api import GoogleSearchService
from services.reddit_service import RedditService
from services.data_service import DataService
from models.reddit_models import RedditPost, RedditComment
from utils.logger_utils import get_logger


@dataclass
class CogBridgesSearchResult:
    """CogBridges搜索结果"""
    query: str
    session_id: str
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 步骤1: Google搜索结果
    google_results: List[Dict[str, Any]] = field(default_factory=list)
    google_search_time: float = 0.0
    
    # 步骤2: Reddit帖子数据
    reddit_posts: List[Dict[str, Any]] = field(default_factory=list)
    reddit_posts_time: float = 0.0
    
    # 步骤3: 评论者历史数据
    commenters_history: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    commenters_history_time: float = 0.0
    
    # 总体统计
    total_time: float = 0.0
    success: bool = True
    error_message: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "query": self.query,
            "session_id": self.session_id,
            "timestamp": self.timestamp.isoformat(),
            "google_results": self.google_results,
            "google_search_time": self.google_search_time,
            "reddit_posts": self.reddit_posts,
            "reddit_posts_time": self.reddit_posts_time,
            "commenters_history": self.commenters_history,
            "commenters_history_time": self.commenters_history_time,
            "total_time": self.total_time,
            "success": self.success,
            "error_message": self.error_message
        }


class CogBridgesService:
    """CogBridges核心业务服务"""
    
    def __init__(self):
        """初始化服务"""
        self.logger = get_logger(__name__)
        
        # 初始化子服务
        self.google_service = GoogleSearchService()
        self.reddit_service = RedditService()
        self.data_service = DataService()
        
        # 业务参数（可配置）
        self.max_search_results = 5  # 前5个搜索结果
        self.max_comments_per_post = 6  # 每个帖子前6个评论
        self.max_user_comments = 20  # 用户历史前20个评论
        self.max_user_posts = 10  # 用户历史前10个帖子
        
        self.logger.info("CogBridges核心业务服务初始化成功")
    
    async def search(self, query: str) -> CogBridgesSearchResult:
        """
        执行完整的CogBridges搜索流程
        
        Args:
            query: 搜索查询
            
        Returns:
            完整的搜索结果
        """
        start_time = time.time()
        session_id = self.data_service.generate_session_id(query)
        
        result = CogBridgesSearchResult(
            query=query,
            session_id=session_id
        )
        
        try:
            self.logger.info(f"开始CogBridges搜索流程: {query}")
            
            # 步骤1: Google搜索（默认限制在Reddit）
            google_results = await self._step1_google_search(query)
            result.google_results = google_results["results"]
            result.google_search_time = google_results["search_time"]
            
            if not result.google_results:
                result.success = False
                result.error_message = "Google搜索未找到Reddit结果"
                return result
            
            # 步骤2: 并行获取Reddit帖子内容和评论
            reddit_data = await self._step2_get_reddit_posts(result.google_results)
            result.reddit_posts = reddit_data["posts"]
            result.reddit_posts_time = reddit_data["processing_time"]
            
            if not result.reddit_posts:
                result.success = False
                result.error_message = "未能获取Reddit帖子数据"
                return result
            
            # 步骤3: 并行获取评论者历史数据
            commenters_data = await self._step3_get_commenters_history(result.reddit_posts)
            result.commenters_history = commenters_data["history"]
            result.commenters_history_time = commenters_data["processing_time"]
            
            # 计算总时间
            result.total_time = time.time() - start_time
            
            # 保存完整结果
            await self._save_results(result)
            
            self.logger.info(f"CogBridges搜索完成: {query}, 总耗时: {result.total_time:.2f}秒")
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            result.total_time = time.time() - start_time
            self.logger.error(f"CogBridges搜索失败: {e}")
        
        return result
    
    async def _step1_google_search(self, query: str) -> Dict[str, Any]:
        """步骤1: 使用Google API搜索Reddit内容"""
        self.logger.info(f"步骤1: Google搜索Reddit内容 - {query}")
        start_time = time.time()
        
        # 使用Google Custom Search API搜索Reddit
        search_result = self.google_service.search(
            query=query,
            max_results=self.max_search_results,
            site_filter="site:reddit.com"
        )
        
        if not search_result.success:
            raise Exception(f"Google搜索失败: {search_result.error_message}")
        
        # 过滤出Reddit帖子URL
        reddit_urls = []
        for result in search_result.results:
            if result.is_reddit_url and "/comments/" in result.url:
                reddit_urls.append({
                    "title": result.title,
                    "url": result.url,
                    "snippet": result.snippet,
                    "rank": result.rank
                })
        
        search_time = time.time() - start_time
        self.logger.info(f"步骤1完成: 找到 {len(reddit_urls)} 个Reddit帖子, 耗时: {search_time:.2f}秒")
        
        return {
            "results": reddit_urls[:self.max_search_results],
            "search_time": search_time
        }
    
    async def _step2_get_reddit_posts(self, google_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """步骤2: 并行获取Reddit帖子内容和评论"""
        self.logger.info(f"步骤2: 并行获取 {len(google_results)} 个Reddit帖子的内容和评论")
        start_time = time.time()
        
        # 创建并行任务
        tasks = []
        for result in google_results:
            task = self._get_single_post_data(result["url"], result)
            tasks.append(task)
        
        # 并行执行
        posts_data = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        valid_posts = []
        for data in posts_data:
            if isinstance(data, dict) and data.get("success"):
                valid_posts.append(data)
            elif isinstance(data, Exception):
                self.logger.warning(f"获取帖子数据失败: {data}")
        
        processing_time = time.time() - start_time
        self.logger.info(f"步骤2完成: 成功获取 {len(valid_posts)} 个帖子数据, 耗时: {processing_time:.2f}秒")
        
        return {
            "posts": valid_posts,
            "processing_time": processing_time
        }
    
    async def _get_single_post_data(self, url: str, google_result: Dict[str, Any]) -> Dict[str, Any]:
        """获取单个帖子的完整数据"""
        try:
            # 获取帖子详情
            post_details = await self.reddit_service.get_post_details(url)
            if not post_details:
                return {"success": False, "error": "帖子不存在"}
            
            # 获取评论
            comments = await self.reddit_service.get_post_comments(
                url, 
                limit=self.max_comments_per_post
            )
            
            return {
                "success": True,
                "google_result": google_result,
                "post": {
                    "id": post_details.id,
                    "title": post_details.title,
                    "author": post_details.author,
                    "score": post_details.score,
                    "num_comments": post_details.num_comments,
                    "subreddit": post_details.subreddit,
                    "url": post_details.url,
                    "created_utc": post_details.created_utc,
                    "selftext": getattr(post_details, 'selftext', '')
                },
                "comments": [
                    {
                        "id": c.id,
                        "author": c.author,
                        "body": c.body,
                        "score": c.score,
                        "created_utc": c.created_utc
                    } for c in comments
                ],
                "commenters": list(set(c.author for c in comments if c.author != "[deleted]"))
            }
            
        except Exception as e:
            self.logger.warning(f"获取帖子数据失败 {url}: {e}")
            return {"success": False, "error": str(e)}
    
    async def _step3_get_commenters_history(self, posts_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """步骤3: 并行获取所有评论者的历史数据"""
        self.logger.info("步骤3: 并行获取评论者历史数据")
        start_time = time.time()
        
        # 收集所有评论者和对应的子版块
        commenters_subreddits = {}
        for post_data in posts_data:
            subreddit = post_data["post"]["subreddit"]
            for commenter in post_data["commenters"]:
                if commenter not in commenters_subreddits:
                    commenters_subreddits[commenter] = set()
                commenters_subreddits[commenter].add(subreddit)
        
        self.logger.info(f"需要获取 {len(commenters_subreddits)} 个评论者的历史数据")
        
        # 创建并行任务
        tasks = []
        for commenter, subreddits in commenters_subreddits.items():
            for subreddit in subreddits:
                task = self._get_user_history_in_subreddit(commenter, subreddit)
                tasks.append(task)
        
        # 并行执行
        history_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 整理结果
        commenters_history = {}
        for result in history_results:
            if isinstance(result, dict) and result.get("success"):
                username = result["username"]
                subreddit = result["subreddit"]
                
                if username not in commenters_history:
                    commenters_history[username] = {}
                
                commenters_history[username][subreddit] = {
                    "comments": result["comments"],
                    "posts": result["posts"]
                }
            elif isinstance(result, Exception):
                self.logger.warning(f"获取用户历史失败: {result}")
        
        processing_time = time.time() - start_time
        self.logger.info(f"步骤3完成: 获取了 {len(commenters_history)} 个用户的历史数据, 耗时: {processing_time:.2f}秒")
        
        return {
            "history": commenters_history,
            "processing_time": processing_time
        }
    
    async def _get_user_history_in_subreddit(self, username: str, subreddit: str) -> Dict[str, Any]:
        """获取用户在特定子版块的历史数据"""
        try:
            # 获取用户历史评论和帖子
            user_comments = await self.reddit_service.get_user_comments_in_subreddit(
                username, subreddit, limit=self.max_user_comments
            )
            
            user_posts = await self.reddit_service.get_user_posts_in_subreddit(
                username, subreddit, limit=self.max_user_posts
            )
            
            return {
                "success": True,
                "username": username,
                "subreddit": subreddit,
                "comments": [
                    {
                        "id": c.id,
                        "body": c.body[:200] + "..." if len(c.body) > 200 else c.body,
                        "score": c.score,
                        "created_utc": c.created_utc
                    } for c in user_comments
                ],
                "posts": [
                    {
                        "id": p.id,
                        "title": p.title,
                        "score": p.score,
                        "created_utc": p.created_utc
                    } for p in user_posts
                ]
            }
            
        except Exception as e:
            self.logger.warning(f"获取用户历史失败 {username} in {subreddit}: {e}")
            return {"success": False, "error": str(e)}
    
    async def _save_results(self, result: CogBridgesSearchResult):
        """保存完整的搜索结果"""
        try:
            # 保存到JSON文件
            filename = f"cogbridges_search_{result.session_id}.json"
            filepath = self.data_service.data_dir / "results" / filename
            
            import json
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(result.to_dict(), f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"搜索结果已保存: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存搜索结果失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            "google_stats": self.google_service.get_statistics(),
            "reddit_stats": self.reddit_service.get_statistics(),
            "business_config": {
                "max_search_results": self.max_search_results,
                "max_comments_per_post": self.max_comments_per_post,
                "max_user_comments": self.max_user_comments,
                "max_user_posts": self.max_user_posts
            }
        }
