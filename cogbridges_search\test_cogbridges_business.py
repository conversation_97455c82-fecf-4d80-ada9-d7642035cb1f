#!/usr/bin/env python3
"""
CogBridges Search - 完整业务流程测试
测试端到端的真实业务流程：Google搜索 -> Reddit帖子获取 -> 评论者历史数据获取
"""

import sys
import os
import asyncio
import json
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services import CogBridgesService
from config import config


async def test_cogbridges_business():
    """测试完整的CogBridges业务流程"""
    print("🚀 CogBridges Search - 完整业务流程测试")
    print("=" * 60)
    
    # 检查配置
    print("📋 检查配置...")
    print(f"Google Custom Search API配置: {config.google_search_configured}")
    print(f"Reddit API配置: {config.reddit_configured}")
    
    if not config.google_search_configured:
        print("❌ Google Custom Search API未配置")
        return False
    
    if not config.reddit_configured:
        print("❌ Reddit API未配置")
        return False
    
    # 初始化CogBridges服务
    print("\n🔧 初始化CogBridges服务...")
    try:
        cogbridges = CogBridgesService()
        print("✅ CogBridges服务初始化成功")
    except Exception as e:
        print(f"❌ CogBridges服务初始化失败: {e}")
        return False
    
    # 测试查询列表
    test_queries = [
        "python programming tips",
        "machine learning tutorial", 
        "web development best practices"
    ]
    
    all_results = []
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{'='*60}")
        print(f"🔍 测试查询 {i}/{len(test_queries)}: {query}")
        print(f"{'='*60}")
        
        try:
            # 执行完整的业务流程
            result = await cogbridges.search(query)
            
            if result.success:
                print(f"✅ 业务流程完成成功!")
                print(f"📊 流程统计:")
                print(f"  总耗时: {result.total_time:.2f}秒")
                print(f"  Google搜索耗时: {result.google_search_time:.2f}秒")
                print(f"  Reddit帖子获取耗时: {result.reddit_posts_time:.2f}秒")
                print(f"  评论者历史获取耗时: {result.commenters_history_time:.2f}秒")
                
                print(f"\n📈 数据统计:")
                print(f"  Google搜索结果: {len(result.google_results)} 个")
                print(f"  Reddit帖子: {len(result.reddit_posts)} 个")
                print(f"  评论者历史: {len(result.commenters_history)} 个用户")
                
                # 显示详细结果
                print(f"\n📋 详细结果:")
                
                # Google搜索结果
                print(f"  🔍 Google搜索结果:")
                for j, google_result in enumerate(result.google_results, 1):
                    print(f"    {j}. {google_result['title'][:60]}...")
                    print(f"       URL: {google_result['url']}")
                
                # Reddit帖子数据
                print(f"  📱 Reddit帖子数据:")
                for j, post_data in enumerate(result.reddit_posts, 1):
                    if post_data.get("success"):
                        post = post_data["post"]
                        comments = post_data["comments"]
                        commenters = post_data["commenters"]
                        
                        print(f"    {j}. {post['title'][:50]}...")
                        print(f"       作者: {post['author']}, 分数: {post['score']}")
                        print(f"       评论数: {len(comments)}, 评论者: {len(commenters)}")
                
                # 评论者历史数据
                print(f"  👥 评论者历史数据:")
                for username, history in result.commenters_history.items():
                    total_comments = sum(len(data.get("comments", [])) for data in history.values())
                    total_posts = sum(len(data.get("posts", [])) for data in history.values())
                    subreddits = list(history.keys())
                    
                    print(f"    {username}: {total_comments}条评论, {total_posts}个帖子")
                    print(f"      子版块: {', '.join(subreddits)}")
                
                all_results.append(result.to_dict())
                
            else:
                print(f"❌ 业务流程失败: {result.error_message}")
                print(f"⏱️ 失败前耗时: {result.total_time:.2f}秒")
                
        except Exception as e:
            print(f"❌ 测试查询失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 保存所有测试结果
    print(f"\n💾 保存测试结果...")
    try:
        test_results = {
            "test_info": {
                "test_name": "CogBridges Complete Business Flow Test",
                "timestamp": datetime.now().isoformat(),
                "total_queries": len(test_queries),
                "successful_queries": len(all_results)
            },
            "queries": test_queries,
            "results": all_results,
            "statistics": cogbridges.get_statistics()
        }
        
        # 保存到文件
        results_dir = project_root / "data" / "results"
        results_dir.mkdir(parents=True, exist_ok=True)
        
        filename = f"cogbridges_business_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        filepath = results_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)
        
        file_size = os.path.getsize(filepath) / 1024
        print(f"✅ 测试结果已保存: {filepath}")
        print(f"📊 文件大小: {file_size:.1f} KB")
        
    except Exception as e:
        print(f"❌ 保存测试结果失败: {e}")
    
    # 显示最终统计
    print(f"\n📊 最终统计:")
    stats = cogbridges.get_statistics()
    
    print(f"Google搜索统计:")
    for key, value in stats["google_stats"].items():
        print(f"  {key}: {value}")
    
    print(f"Reddit服务统计:")
    for key, value in stats["reddit_stats"].items():
        print(f"  {key}: {value}")
    
    print(f"业务配置:")
    for key, value in stats["business_config"].items():
        print(f"  {key}: {value}")
    
    success_rate = len(all_results) / len(test_queries) * 100
    print(f"\n🎯 测试结果:")
    print(f"  总查询数: {len(test_queries)}")
    print(f"  成功查询数: {len(all_results)}")
    print(f"  成功率: {success_rate:.1f}%")
    
    if success_rate >= 100:
        print(f"\n🎉 所有业务流程测试通过！")
        return True
    elif success_rate >= 50:
        print(f"\n⚠️ 部分业务流程测试通过")
        return True
    else:
        print(f"\n❌ 业务流程测试失败")
        return False


def test_single_query():
    """测试单个查询的详细流程"""
    print("🔍 单个查询详细测试")
    print("=" * 40)
    
    async def run_single_test():
        cogbridges = CogBridgesService()
        
        # 使用一个简单的查询进行详细测试
        query = "python tutorial"
        print(f"查询: {query}")
        
        result = await cogbridges.search(query)
        
        if result.success:
            print("✅ 查询成功")
            
            # 显示每个步骤的详细信息
            print(f"\n步骤1 - Google搜索:")
            print(f"  耗时: {result.google_search_time:.2f}秒")
            print(f"  结果数: {len(result.google_results)}")
            
            print(f"\n步骤2 - Reddit帖子获取:")
            print(f"  耗时: {result.reddit_posts_time:.2f}秒")
            print(f"  成功帖子数: {len(result.reddit_posts)}")
            
            print(f"\n步骤3 - 评论者历史获取:")
            print(f"  耗时: {result.commenters_history_time:.2f}秒")
            print(f"  评论者数: {len(result.commenters_history)}")
            
            print(f"\n总耗时: {result.total_time:.2f}秒")
            
        else:
            print(f"❌ 查询失败: {result.error_message}")
    
    asyncio.run(run_single_test())


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CogBridges业务流程测试")
    parser.add_argument("--single", action="store_true", help="运行单个查询测试")
    args = parser.parse_args()
    
    try:
        if args.single:
            test_single_query()
        else:
            success = asyncio.run(test_cogbridges_business())
            if success:
                print("\n✅ CogBridges业务流程测试完成，系统正常")
                sys.exit(0)
            else:
                print("\n❌ CogBridges业务流程测试失败")
                sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
