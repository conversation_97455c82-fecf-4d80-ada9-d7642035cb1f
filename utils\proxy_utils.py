"""
CogBridges Search - 代理配置工具
提供代理设置和会话管理功能
"""

import os
import requests
import aiohttp
from typing import Optional, Dict, Any
from config import config
from utils.logger_utils import get_logger

logger = get_logger(__name__)


def setup_proxy() -> Optional[Dict[str, str]]:
    """
    设置代理配置
    
    Returns:
        代理配置字典，如果未配置则返回None
    """
    proxy_dict = config.proxy_dict
    
    if proxy_dict:
        # 设置环境变量，确保所有HTTP库都能使用代理
        if "http" in proxy_dict:
            os.environ["HTTP_PROXY"] = proxy_dict["http"]
        if "https" in proxy_dict:
            os.environ["HTTPS_PROXY"] = proxy_dict["https"]
        
        logger.info(f"代理配置已设置: {proxy_dict}")
        return proxy_dict
    else:
        logger.info("未配置代理")
        return None


def get_proxy_session() -> requests.Session:
    """
    获取配置了代理的requests会话
    
    Returns:
        配置了代理的requests.Session对象
    """
    session = requests.Session()
    
    proxy_dict = setup_proxy()
    if proxy_dict:
        session.proxies.update(proxy_dict)
        logger.debug("requests会话已配置代理")
    
    # 设置通用请求头
    session.headers.update({
        'User-Agent': 'CogBridges-Search/1.0 (Reddit Analysis Tool)',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
    })
    
    # 设置超时
    session.timeout = config.REQUEST_TIMEOUT
    
    return session


async def get_async_proxy_session() -> aiohttp.ClientSession:
    """
    获取配置了代理的异步HTTP会话
    
    Returns:
        配置了代理的aiohttp.ClientSession对象
    """
    proxy_dict = setup_proxy()
    
    # 创建连接器
    connector_kwargs = {
        'limit': config.MAX_CONCURRENT_REQUESTS,
        'limit_per_host': 5,
        'ttl_dns_cache': 300,
        'use_dns_cache': True,
    }
    
    # 如果配置了代理，设置代理连接器
    if proxy_dict and proxy_dict.get("http"):
        connector_kwargs['trust_env'] = True
        logger.debug("aiohttp会话已配置代理")
    
    connector = aiohttp.TCPConnector(**connector_kwargs)
    
    # 设置超时
    timeout = aiohttp.ClientTimeout(
        total=config.REQUEST_TIMEOUT,
        connect=10,
        sock_read=config.REQUEST_TIMEOUT
    )
    
    # 设置请求头
    headers = {
        'User-Agent': 'CogBridges-Search/1.0 (Reddit Analysis Tool)',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
    }
    
    session = aiohttp.ClientSession(
        connector=connector,
        timeout=timeout,
        headers=headers,
        trust_env=True  # 使用环境变量中的代理设置
    )
    
    return session


def test_proxy_connection(test_url: str = "https://httpbin.org/ip") -> bool:
    """
    测试代理连接是否正常
    
    Args:
        test_url: 测试URL
        
    Returns:
        连接是否成功
    """
    try:
        session = get_proxy_session()
        response = session.get(test_url, timeout=10)
        
        if response.status_code == 200:
            logger.info("代理连接测试成功")
            
            # 如果是httpbin，显示IP信息
            if "httpbin.org" in test_url:
                try:
                    ip_info = response.json()
                    logger.info(f"当前IP: {ip_info.get('origin', 'Unknown')}")
                except:
                    pass
            
            return True
        else:
            logger.warning(f"代理连接测试失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"代理连接测试异常: {e}")
        return False


async def test_async_proxy_connection(test_url: str = "https://httpbin.org/ip") -> bool:
    """
    测试异步代理连接是否正常
    
    Args:
        test_url: 测试URL
        
    Returns:
        连接是否成功
    """
    try:
        async with await get_async_proxy_session() as session:
            async with session.get(test_url) as response:
                if response.status == 200:
                    logger.info("异步代理连接测试成功")
                    
                    # 如果是httpbin，显示IP信息
                    if "httpbin.org" in test_url:
                        try:
                            ip_info = await response.json()
                            logger.info(f"当前IP: {ip_info.get('origin', 'Unknown')}")
                        except:
                            pass
                    
                    return True
                else:
                    logger.warning(f"异步代理连接测试失败: HTTP {response.status}")
                    return False
                    
    except Exception as e:
        logger.error(f"异步代理连接测试异常: {e}")
        return False


def get_proxy_info() -> Dict[str, Any]:
    """
    获取代理配置信息
    
    Returns:
        代理配置信息字典
    """
    proxy_dict = config.proxy_dict
    
    info = {
        "proxy_configured": config.proxy_configured,
        "proxy_settings": proxy_dict,
        "environment_variables": {
            "HTTP_PROXY": os.environ.get("HTTP_PROXY", ""),
            "HTTPS_PROXY": os.environ.get("HTTPS_PROXY", ""),
        }
    }
    
    return info


if __name__ == "__main__":
    # 测试代理配置
    print("CogBridges Search - 代理配置测试")
    print("=" * 50)
    
    # 显示代理信息
    proxy_info = get_proxy_info()
    print("📡 代理配置信息:")
    for key, value in proxy_info.items():
        print(f"  {key}: {value}")
    
    # 测试代理连接
    print("\n🔗 测试代理连接...")
    success = test_proxy_connection()
    
    if success:
        print("✅ 代理连接正常")
    else:
        print("❌ 代理连接失败")
        print("请检查代理设置和网络连接")
