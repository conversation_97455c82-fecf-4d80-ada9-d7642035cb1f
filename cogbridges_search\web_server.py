#!/usr/bin/env python3
"""
CogBridges Search - Web服务器
提供搜索页面和API接口
"""

import sys
import os
import asyncio
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services import CogBridgesService
from config import config


# 请求模型
class SearchRequest(BaseModel):
    query: str
    reddit_only: bool = True
    include_comments: bool = True
    user_history: bool = True
    max_results: int = 5
    max_comments: int = 6


# 创建FastAPI应用
app = FastAPI(
    title="CogBridges Search API",
    description="智能搜索引擎API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
cogbridges_service = None


@app.on_event("startup")
async def startup_event():
    """应用启动时初始化服务"""
    global cogbridges_service
    
    print("🚀 启动CogBridges搜索服务...")
    
    # 检查配置
    if not config.reddit_configured:
        print("⚠️ Reddit API未配置，某些功能可能受限")
    
    if not config.google_search_configured:
        print("⚠️ Google Custom Search API未配置，将使用模拟数据")
    
    try:
        cogbridges_service = CogBridgesService()
        print("✅ CogBridges服务初始化成功")
    except Exception as e:
        print(f"❌ CogBridges服务初始化失败: {e}")
        cogbridges_service = None


# 静态文件服务
app.mount("/static", StaticFiles(directory=project_root / "web"), name="static")


@app.get("/", response_class=HTMLResponse)
async def index():
    """返回搜索页面"""
    html_file = project_root / "web" / "index.html"
    
    if not html_file.exists():
        raise HTTPException(status_code=404, detail="搜索页面未找到")
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    return HTMLResponse(content=html_content)


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "cogbridges": cogbridges_service is not None,
            "google_api": config.google_search_configured,
            "reddit_api": config.reddit_configured
        }
    }


@app.post("/api/search")
async def search_api(request: SearchRequest):
    """搜索API接口"""
    if cogbridges_service is None:
        raise HTTPException(status_code=503, detail="搜索服务未初始化")
    
    try:
        # 更新服务配置
        cogbridges_service.max_search_results = request.max_results
        cogbridges_service.max_comments_per_post = request.max_comments
        
        # 执行搜索
        result = await cogbridges_service.search(request.query)
        
        if result.success:
            return JSONResponse(content={
                "success": True,
                "data": {
                    "query": result.query,
                    "session_id": result.session_id,
                    "timestamp": result.timestamp.isoformat(),
                    "google_results": result.google_results,
                    "reddit_posts": result.reddit_posts,
                    "commenters_history": result.commenters_history,
                    "statistics": {
                        "total_time": result.total_time,
                        "google_search_time": result.google_search_time,
                        "reddit_posts_time": result.reddit_posts_time,
                        "commenters_history_time": result.commenters_history_time
                    }
                }
            })
        else:
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "error": result.error_message,
                    "statistics": {
                        "total_time": result.total_time
                    }
                }
            )
            
    except Exception as e:
        print(f"搜索API错误: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@app.get("/api/suggestions")
async def get_suggestions(q: str = ""):
    """获取搜索建议"""
    # 预定义的搜索建议
    suggestions = [
        'python programming tips',
        'machine learning tutorial',
        'web development best practices',
        'data science career advice',
        'artificial intelligence news',
        'javascript frameworks comparison',
        'react vs vue',
        'docker tutorial',
        'kubernetes basics',
        'aws certification guide',
        'remote work tips',
        'startup advice',
        'investment strategies',
        'cryptocurrency explained',
        'fitness motivation'
    ]
    
    if not q:
        return {"suggestions": suggestions[:5]}
    
    # 过滤建议
    filtered = [s for s in suggestions if q.lower() in s.lower()]
    
    return {"suggestions": filtered[:5]}


@app.get("/api/statistics")
async def get_statistics():
    """获取服务统计信息"""
    if cogbridges_service is None:
        raise HTTPException(status_code=503, detail="搜索服务未初始化")
    
    return cogbridges_service.get_statistics()


@app.get("/api/results/{session_id}")
async def get_search_results(session_id: str):
    """获取搜索结果"""
    results_dir = project_root / "data" / "results"
    result_file = results_dir / f"cogbridges_search_{session_id}.json"
    
    if not result_file.exists():
        raise HTTPException(status_code=404, detail="搜索结果未找到")
    
    try:
        with open(result_file, 'r', encoding='utf-8') as f:
            result_data = json.load(f)
        
        return JSONResponse(content=result_data)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取搜索结果失败: {str(e)}")


@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    """404错误处理"""
    return JSONResponse(
        status_code=404,
        content={"error": "页面未找到", "path": str(request.url.path)}
    )


@app.exception_handler(500)
async def internal_error_handler(request: Request, exc: HTTPException):
    """500错误处理"""
    return JSONResponse(
        status_code=500,
        content={"error": "服务器内部错误", "detail": str(exc.detail)}
    )


def main():
    """启动Web服务器"""
    print("🌐 启动CogBridges Web服务器...")
    print("=" * 50)
    
    # 检查配置
    print("📋 检查配置...")
    print(f"Google Custom Search API: {'✅' if config.google_search_configured else '❌'}")
    print(f"Reddit API: {'✅' if config.reddit_configured else '❌'}")
    
    # 确保必要的目录存在
    web_dir = project_root / "web"
    data_dir = project_root / "data" / "results"
    data_dir.mkdir(parents=True, exist_ok=True)
    
    if not web_dir.exists():
        print("❌ Web目录不存在")
        return
    
    print(f"\n🚀 服务器启动中...")
    print(f"📁 Web目录: {web_dir}")
    print(f"💾 数据目录: {data_dir}")
    print(f"🌐 访问地址: http://localhost:8000")
    print(f"📚 API文档: http://localhost:8000/docs")
    print(f"❤️ 健康检查: http://localhost:8000/health")
    
    # 启动服务器
    uvicorn.run(
        "web_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        reload_dirs=[str(project_root)],
        log_level="info"
    )


if __name__ == "__main__":
    main()
