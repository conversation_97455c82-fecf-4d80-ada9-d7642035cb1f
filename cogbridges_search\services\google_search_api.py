"""
CogBridges Search - Google搜索服务
使用Google Custom Search API实现搜索，支持Reddit内容搜索
"""

import time
from typing import List, Dict, Any
import requests
from tenacity import retry, stop_after_attempt, wait_exponential

from config import config
from models.search_models import SearchQuery, SearchResult, GoogleSearchResult
from utils.logger_utils import get_logger, log_api_call, log_search_operation
from utils.proxy_utils import get_proxy_session


class GoogleSearchService:
    """Google搜索服务类 - 使用Google Custom Search API"""

    def __init__(self):
        """初始化Google搜索服务"""
        self.logger = get_logger(__name__)

        # 验证API配置
        if not config.google_search_configured:
            self.logger.error("Google Custom Search API未配置")
            raise ValueError("Google Custom Search API配置不完整")

        # Google Custom Search API配置
        self.api_key = config.GOOGLE_SEARCH_API_KEY
        self.engine_id = config.GOOGLE_SEARCH_ENGINE_ID
        self.api_url = "https://www.googleapis.com/customsearch/v1"

        # 请求统计
        self.request_count = 0
        self.total_search_time = 0.0

        self.logger.info("Google搜索服务初始化成功（Custom Search API模式）")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    @log_api_call("Google Custom Search", "search", "GET")
    def search(
        self,
        query: str,
        max_results: int = None,
        site_filter: str = "site:reddit.com",
        **kwargs
    ) -> SearchResult:
        """
        执行Google搜索

        Args:
            query: 搜索查询
            max_results: 最大结果数量
            site_filter: 站点过滤器
            **kwargs: 其他搜索参数

        Returns:
            搜索结果对象
        """
        start_time = time.time()

        # 创建搜索查询对象
        search_query = SearchQuery(
            query=query,
            max_results=max_results or config.GOOGLE_SEARCH_RESULTS_COUNT,
            site_filter=site_filter
        )

        self.logger.info(f"开始Google Custom Search API搜索: {query}")

        try:
            # 构建搜索查询字符串
            if site_filter:
                full_query = f"{query} {site_filter}"
            else:
                full_query = query

            # 构建API请求参数
            params = {
                'key': self.api_key,
                'cx': self.engine_id,
                'q': full_query,
                'num': min(search_query.max_results, 10),  # Custom Search API限制每次最多10个结果
                'safe': 'off',
                'fields': 'items(title,link,snippet,displayLink),searchInformation(totalResults)'
            }

            self.logger.debug(f"API请求参数: {params}")

            # 获取代理会话
            session = get_proxy_session()

            # 发送API请求
            response = session.get(
                self.api_url,
                params=params,
                timeout=30
            )
            response.raise_for_status()

            # 解析API响应
            api_data = response.json()
            search_results = self._parse_api_results(api_data)

            # 计算搜索时间
            search_time = time.time() - start_time

            # 更新统计信息
            self.request_count += 1
            self.total_search_time += search_time

            total_results = int(api_data.get('searchInformation', {}).get('totalResults', 0))

            self.logger.info(f"Google搜索完成: 找到 {len(search_results)} 个结果（总计 {total_results}），耗时 {search_time:.2f}秒")

            return SearchResult(
                query=search_query,
                results=search_results,
                total_results=total_results,
                search_time=search_time,
                success=True
            )

        except Exception as e:
            search_time = time.time() - start_time
            self.logger.error(f"Google搜索失败: {e}")

            return SearchResult(
                query=SearchQuery(query=query, max_results=max_results or 10, site_filter=site_filter),
                results=[],
                total_results=0,
                search_time=search_time,
                success=False,
                error_message=str(e)
            )

    def _parse_api_results(self, api_data: Dict[str, Any]) -> List[GoogleSearchResult]:
        """
        解析Google Custom Search API响应

        Args:
            api_data: API响应数据

        Returns:
            搜索结果列表
        """
        results = []
        items = api_data.get('items', [])

        for i, item in enumerate(items, 1):
            try:
                title = item.get('title', '')
                url = item.get('link', '')
                snippet = item.get('snippet', '')
                display_link = item.get('displayLink', '')



                result = GoogleSearchResult(
                    title=title,
                    url=url,
                    snippet=snippet,
                    display_url=display_link,
                    rank=i
                )

                results.append(result)

            except Exception as e:
                self.logger.warning(f"解析搜索结果项失败: {e}")
                continue

        return results

    def get_search_suggestions(self, query: str) -> List[str]:
        """
        获取搜索建议（Google Custom Search API不直接支持，返回空列表）

        Args:
            query: 搜索查询

        Returns:
            建议列表（空）
        """
        self.logger.info("Google Custom Search API不支持搜索建议功能")
        return []

    def test_connection(self) -> bool:
        """
        测试API连接

        Returns:
            连接是否成功
        """
        try:
            # 执行一个简单的测试搜索，使用这个CS搜索引擎能找到的内容
            test_result = self.search("lectures", max_results=1, site_filter="")
            return test_result.success and len(test_result.results) > 0

        except Exception as e:
            self.logger.error(f"API连接测试失败: {e}")
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取服务统计信息

        Returns:
            统计信息字典
        """
        return {
            'request_count': self.request_count,
            'total_search_time': self.total_search_time,
            'average_search_time': (
                self.total_search_time / self.request_count
                if self.request_count > 0 else 0
            ),
            'search_method': 'Google Custom Search API',
            'api_configured': config.google_search_configured
        }
