#!/usr/bin/env python3
"""
CogBridges Search - 完整系统测试
测试Google Custom Search API、Reddit API和数据保存功能
"""

import sys
import os
import asyncio
import json
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services import GoogleSearchService, RedditService, DataService
from utils.proxy_utils import get_proxy_info
from config import config


async def test_complete_system():
    """测试完整系统功能"""
    print("🚀 CogBridges Search - 完整系统测试")
    print("=" * 60)
    
    # 1. 检查配置
    print("📋 检查配置...")
    print(f"Google Custom Search API配置: {config.google_search_configured}")
    print(f"Reddit API配置: {config.reddit_configured}")
    
    proxy_info = get_proxy_info()
    print(f"代理已配置: {proxy_info['proxy_configured']}")
    
    # 2. 测试Google Custom Search API
    print(f"\n🔍 测试Google Custom Search API...")
    try:
        google_service = GoogleSearchService()
        print("✅ Google搜索服务初始化成功")
        
        # 测试连接
        print("🔗 测试Google API连接...")
        if google_service.test_connection():
            print("✅ Google API连接正常")
        else:
            print("❌ Google API连接失败")
            return False
        
        # 执行实际搜索 - 搜索CS课程相关内容
        print("🔍 执行实际搜索测试...")
        search_queries = [
            "lectures",
            "assignments", 
            "algorithms"
        ]
        
        all_search_results = []
        
        for query in search_queries:
            print(f"\n  搜索: {query}")
            # 不使用site:reddit.com过滤，因为这个搜索引擎是CS课程专用的
            search_result = google_service.search(query, max_results=3, site_filter="")
            
            if search_result.success:
                print(f"  ✅ 搜索成功: 找到 {len(search_result.results)} 个结果")
                for i, result in enumerate(search_result.results, 1):
                    print(f"    {i}. {result.title[:60]}...")
                    print(f"       URL: {result.url}")
                    print(f"       显示链接: {result.display_url}")
                
                all_search_results.append({
                    "query": query,
                    "results": [
                        {
                            "title": r.title,
                            "url": r.url,
                            "snippet": r.snippet,
                            "rank": r.rank,
                            "display_url": r.display_url,
                            "is_reddit_url": r.is_reddit_url
                        } for r in search_result.results
                    ],
                    "total_results": search_result.total_results,
                    "search_time": search_result.search_time
                })
            else:
                print(f"  ❌ 搜索失败: {search_result.error_message}")
                return False
            
    except Exception as e:
        print(f"❌ Google搜索服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 3. 测试Reddit服务
    print(f"\n📱 测试Reddit服务...")
    try:
        reddit_service = RedditService()
        print("✅ Reddit服务初始化成功")
        
        if not reddit_service.configured:
            print("❌ Reddit API未正确配置")
            return False
        
        # 测试连接
        print("🔗 测试Reddit API连接...")
        if reddit_service.test_api_connection():
            print("✅ Reddit API连接正常")
        else:
            print("❌ Reddit API连接失败")
            return False
        
        # 测试URL解析功能
        print("🔍 测试URL解析功能...")
        test_urls = [
            "https://www.reddit.com/r/Python/comments/abc123/test_post/",
            "https://reddit.com/r/programming/",
            "https://www.reddit.com/user/testuser/"
        ]
        
        url_parsing_results = []
        for url in test_urls:
            result = reddit_service.parse_reddit_url(url)
            print(f"  URL: {url}")
            print(f"  解析结果: {result}")
            url_parsing_results.append({"url": url, "parsed": result})
        
        # 测试评论者提取功能
        print("👥 测试评论者提取功能...")
        from models.reddit_models import RedditComment
        
        test_comments = [
            RedditComment(
                id="1", body="Great tutorial!", author="user1",
                score=10, created_utc=1640995200, parent_id="t3_abc123",
                subreddit="test", permalink="/test/1"
            ),
            RedditComment(
                id="2", body="Thanks for sharing", author="user2",
                score=5, created_utc=1640995300, parent_id="t3_abc123",
                subreddit="test", permalink="/test/2"
            )
        ]
        
        commenters = reddit_service.extract_commenters(test_comments)
        print(f"  提取到的评论者: {commenters}")
        
        reddit_test_data = {
            "url_parsing": url_parsing_results,
            "commenters_extraction": {
                "test_comments": len(test_comments),
                "extracted_commenters": commenters
            }
        }
        
    except Exception as e:
        print(f"❌ Reddit服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 4. 测试数据服务并保存结果
    print(f"\n💾 测试数据服务并保存结果...")
    try:
        data_service = DataService()
        print("✅ 数据服务初始化成功")
        
        # 生成会话ID
        session_id = data_service.generate_session_id("complete system test")
        print(f"生成会话ID: {session_id}")
        
        # 构建完整的测试数据
        complete_test_data = {
            "test_info": {
                "test_name": "Complete System Test",
                "timestamp": datetime.now().isoformat(),
                "session_id": session_id
            },
            "google_search_results": all_search_results,
            "reddit_test_data": reddit_test_data,
            "statistics": {
                "total_google_queries": len(all_search_results),
                "total_google_results": sum(len(r["results"]) for r in all_search_results),
                "google_api_stats": google_service.get_statistics(),
                "reddit_api_stats": reddit_service.get_statistics()
            },
            "success": True
        }
        
        # 保存Google搜索结果
        from models.search_models import SearchQuery, SearchResult
        
        # 创建一个综合的搜索结果对象
        combined_query = SearchQuery(
            query="complete system test", 
            max_results=10, 
            site_filter=""
        )
        
        # 合并所有搜索结果
        all_results = []
        for search_data in all_search_results:
            for result_data in search_data["results"]:
                from models.search_models import GoogleSearchResult
                result = GoogleSearchResult(
                    title=result_data["title"],
                    url=result_data["url"],
                    snippet=result_data["snippet"],
                    display_url=result_data["display_url"],
                    rank=result_data["rank"]
                )
                all_results.append(result)
        
        combined_search_result = SearchResult(
            query=combined_query,
            results=all_results,
            total_results=sum(r["total_results"] for r in all_search_results),
            search_time=sum(r["search_time"] for r in all_search_results),
            success=True
        )
        
        # 保存搜索结果
        search_filepath = data_service.save_search_result(combined_search_result, session_id)
        print(f"✅ 搜索结果已保存: {search_filepath}")
        
        # 保存Reddit数据
        reddit_filepath = data_service.save_reddit_data(complete_test_data, session_id)
        print(f"✅ Reddit数据已保存: {reddit_filepath}")
        
        # 保存完整的测试数据到JSON文件
        test_data_file = data_service.data_dir / "results" / f"complete_system_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(test_data_file, 'w', encoding='utf-8') as f:
            json.dump(complete_test_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 完整测试数据已保存: {test_data_file}")
        
        # 显示保存的文件大小
        search_size = os.path.getsize(search_filepath) / 1024
        reddit_size = os.path.getsize(reddit_filepath) / 1024
        test_size = os.path.getsize(test_data_file) / 1024
        
        print(f"📊 文件大小:")
        print(f"  搜索结果文件: {search_size:.1f} KB")
        print(f"  Reddit数据文件: {reddit_size:.1f} KB")
        print(f"  完整测试数据文件: {test_size:.1f} KB")
        
    except Exception as e:
        print(f"❌ 数据服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 5. 显示最终统计信息
    print(f"\n📊 最终统计信息:")
    
    google_stats = google_service.get_statistics()
    print(f"Google搜索统计:")
    for key, value in google_stats.items():
        print(f"  {key}: {value}")
    
    reddit_stats = reddit_service.get_statistics()
    print(f"Reddit服务统计:")
    for key, value in reddit_stats.items():
        print(f"  {key}: {value}")
    
    print(f"\n🎉 完整系统测试通过！")
    print(f"📁 所有测试数据已保存到 data/results/ 目录")
    print(f"🔧 系统各组件功能正常，可以投入使用")
    return True


def main():
    """主函数"""
    try:
        success = asyncio.run(test_complete_system())
        if success:
            print("\n✅ 完整系统测试成功，所有功能正常")
            sys.exit(0)
        else:
            print("\n❌ 完整系统测试失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
