#!/usr/bin/env python3
"""
CogBridges Search - 真实API测试
测试Google Custom Search API和Reddit API的真实功能
"""

import sys
import os
import asyncio
import json
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services import GoogleSearchService, RedditService, DataService
from utils.proxy_utils import get_proxy_info
from config import config


async def test_real_apis():
    """测试真实的API连接和功能"""
    print("🚀 CogBridges Search - 真实API测试")
    print("=" * 60)
    
    # 1. 检查配置
    print("📋 检查配置...")
    print(f"Google Custom Search API配置: {config.google_search_configured}")
    print(f"Reddit API配置: {config.reddit_configured}")
    
    proxy_info = get_proxy_info()
    print(f"代理已配置: {proxy_info['proxy_configured']}")
    if proxy_info['proxy_configured']:
        print(f"代理设置: {proxy_info['proxy_settings']}")
    
    # 2. 测试Google Custom Search API
    print(f"\n🔍 测试Google Custom Search API...")
    try:
        google_service = GoogleSearchService()
        print("✅ Google搜索服务初始化成功")
        
        # 测试连接
        print("🔗 测试Google API连接...")
        if google_service.test_connection():
            print("✅ Google API连接正常")
        else:
            print("❌ Google API连接失败")
            return False
        
        # 执行实际搜索 - 搜索Python相关的Reddit内容
        print("🔍 执行实际搜索测试...")
        search_queries = [
            "python programming tips",
            "machine learning tutorial",
            "web development best practices"
        ]
        
        all_search_results = []
        
        for query in search_queries:
            print(f"\n  搜索: {query}")
            search_result = google_service.search(query, max_results=5, site_filter="site:reddit.com")
            
            if search_result.success:
                print(f"  ✅ 搜索成功: 找到 {len(search_result.results)} 个结果")
                for i, result in enumerate(search_result.results, 1):
                    print(f"    {i}. {result.title[:60]}...")
                    print(f"       URL: {result.url}")
                    print(f"       Reddit: {result.is_reddit_url}")
                
                all_search_results.append({
                    "query": query,
                    "results": [
                        {
                            "title": r.title,
                            "url": r.url,
                            "snippet": r.snippet,
                            "rank": r.rank,
                            "is_reddit_url": r.is_reddit_url
                        } for r in search_result.results
                    ],
                    "total_results": search_result.total_results,
                    "search_time": search_result.search_time
                })
            else:
                print(f"  ❌ 搜索失败: {search_result.error_message}")
                return False
            
    except Exception as e:
        print(f"❌ Google搜索服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 3. 测试Reddit服务
    print(f"\n📱 测试Reddit服务...")
    try:
        reddit_service = RedditService()
        print("✅ Reddit服务初始化成功")
        
        if not reddit_service.configured:
            print("❌ Reddit API未正确配置")
            return False
        
        # 测试连接
        print("🔗 测试Reddit API连接...")
        if reddit_service.test_api_connection():
            print("✅ Reddit API连接正常")
        else:
            print("❌ Reddit API连接失败")
            return False
        
        # 从搜索结果中找到Reddit URL并获取详情
        reddit_posts_data = []
        
        for search_data in all_search_results:
            for result in search_data["results"]:
                if result["is_reddit_url"] and "/comments/" in result["url"]:
                    print(f"\n  📝 获取Reddit帖子详情: {result['title'][:50]}...")
                    try:
                        post_details = await reddit_service.get_post_details(result["url"])
                        if post_details:
                            print(f"    ✅ 获取成功: {post_details.title[:50]}...")
                            print(f"       作者: {post_details.author}")
                            print(f"       分数: {post_details.score}")
                            print(f"       评论数: {post_details.num_comments}")
                            
                            # 获取评论
                            print(f"    💬 获取评论...")
                            comments = await reddit_service.get_post_comments(result["url"], limit=3)
                            print(f"       获取到 {len(comments)} 条评论")
                            
                            reddit_posts_data.append({
                                "post": {
                                    "id": post_details.id,
                                    "title": post_details.title,
                                    "author": post_details.author,
                                    "score": post_details.score,
                                    "num_comments": post_details.num_comments,
                                    "subreddit": post_details.subreddit,
                                    "url": post_details.url,
                                    "created_utc": post_details.created_utc
                                },
                                "comments": [
                                    {
                                        "id": c.id,
                                        "author": c.author,
                                        "body": c.body[:200] + "..." if len(c.body) > 200 else c.body,
                                        "score": c.score,
                                        "created_utc": c.created_utc
                                    } for c in comments
                                ]
                            })
                            
                            # 只处理前3个Reddit帖子
                            if len(reddit_posts_data) >= 3:
                                break
                        else:
                            print(f"    ⚠️ 未找到帖子详情")
                    except Exception as e:
                        print(f"    ⚠️ 获取帖子失败: {e}")
            
            if len(reddit_posts_data) >= 3:
                break
        
    except Exception as e:
        print(f"❌ Reddit服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 4. 测试数据服务并保存结果
    print(f"\n💾 测试数据服务并保存结果...")
    try:
        data_service = DataService()
        print("✅ 数据服务初始化成功")
        
        # 保存Google搜索结果
        session_id = data_service.generate_session_id("real api test")
        
        # 构建完整的搜索结果数据
        complete_search_data = {
            "search_queries": all_search_results,
            "reddit_posts": reddit_posts_data,
            "statistics": {
                "total_queries": len(all_search_results),
                "total_reddit_posts": len(reddit_posts_data),
                "test_timestamp": datetime.now().isoformat(),
                "google_api_stats": google_service.get_statistics(),
                "reddit_api_stats": reddit_service.get_statistics()
            },
            "success": True
        }
        
        # 创建一个真实的SearchResult对象
        from models.search_models import SearchQuery, SearchResult

        test_query = SearchQuery(query="real api test", max_results=10, site_filter="site:reddit.com")
        test_search_result = SearchResult(
            query=test_query,
            results=[],
            total_results=len(all_search_results),
            search_time=0,
            success=True
        )

        # 保存搜索结果
        search_filepath = data_service.save_search_result(test_search_result, session_id)
        
        # 保存Reddit数据
        reddit_filepath = data_service.save_reddit_data(complete_search_data, session_id)
        
        # 保存完整的测试数据到JSON文件
        test_data_file = data_service.data_dir / "results" / f"real_api_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(test_data_file, 'w', encoding='utf-8') as f:
            json.dump(complete_search_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 搜索结果已保存: {search_filepath}")
        print(f"✅ Reddit数据已保存: {reddit_filepath}")
        print(f"✅ 完整测试数据已保存: {test_data_file}")
        
    except Exception as e:
        print(f"❌ 数据服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 5. 显示统计信息
    print(f"\n📊 最终统计信息:")
    
    google_stats = google_service.get_statistics()
    print(f"Google搜索统计:")
    for key, value in google_stats.items():
        print(f"  {key}: {value}")
    
    reddit_stats = reddit_service.get_statistics()
    print(f"Reddit服务统计:")
    for key, value in reddit_stats.items():
        print(f"  {key}: {value}")
    
    print(f"\n🎉 所有真实API测试通过！")
    print(f"📁 测试数据已保存到 data/results/ 目录")
    return True


def main():
    """主函数"""
    try:
        success = asyncio.run(test_real_apis())
        if success:
            print("\n✅ 真实API测试完成，所有功能正常")
            sys.exit(0)
        else:
            print("\n❌ 真实API测试失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
