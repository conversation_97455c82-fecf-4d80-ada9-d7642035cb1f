{"test_info": {"test_name": "Complete System Test", "timestamp": "2025-07-18T22:27:29.257868", "session_id": "20250718_222729_f991660d"}, "google_search_results": [{"query": "lectures", "results": [{"title": "EE364b: Lecture Slides and Notes", "url": "http://stanford.edu/class/ee364b/lectures.html", "snippet": "These slides and notes will change and get updated throughout the quarter. Please check this page frequently.", "rank": 1, "display_url": "stanford.edu", "is_reddit_url": false}, {"title": "EE363: Lecture Slides - Stanford", "url": "https://stanford.edu/class/ee363/lectures.html", "snippet": "These lecture slides are still changing, so don't print them yet. Linear quadratic regulator: Discrete-time finite horizon · LQR via Lagrange multipliers.", "rank": 2, "display_url": "stanford.edu", "is_reddit_url": false}, {"title": "Stanford Engineering Everywhere | EE364A - Convex Optimization I", "url": "https://see.stanford.edu/Course/EE364A", "snippet": "Lecture Notes. Introduction, Lecture 1. Convex Sets, Lecture2. Convex Functions, Lectures 3-4. Convex Optimization Problems, Lectures 5-7. Duality, Lectures 8-7.", "rank": 3, "display_url": "see.stanford.edu", "is_reddit_url": false}], "total_results": 632000000, "search_time": 1.5496530532836914}, {"query": "assignments", "results": [{"title": "CIS194: Lecture notes and assignments", "url": "https://www.cis.upenn.edu/~cis1940/fall14/lectures.html", "snippet": "All homework assignments should emerge creatively from the Style guidelines. Homework is due at midnight at the end of the day listed.", "rank": 1, "display_url": "www.cis.upenn.edu", "is_reddit_url": false}, {"title": "CS241: Assignments", "url": "https://student.cs.uwaterloo.ca/~cs241/assignments/", "snippet": "Assignments must be submitted using the Marmoset Submission and Testing Server. Read the section of the course outline on Marmoset usage before submitting.", "rank": 2, "display_url": "student.cs.uwaterloo.ca", "is_reddit_url": false}, {"title": "CIS 670: Assignments and Grading", "url": "https://www.cis.upenn.edu/~sweirich/cis670/02/grading.htm", "snippet": "Grades for this course come from three components: homework, class participation and a semester project. There will be no exams. Homework policy. You should ...", "rank": 3, "display_url": "www.cis.upenn.edu", "is_reddit_url": false}], "total_results": 376000000, "search_time": 2.7204108238220215}, {"query": "algorithms", "results": [{"title": "01:198:344 - Design and Analysis of Computer Algorithms", "url": "https://www.cs.rutgers.edu/academics/undergraduate/course-synopses/course-details/01-198-344-design-and-analysis-of-computer-algorithms", "snippet": "To study a variety of useful algorithms and analyze their complexity; by that experience to gain insight into principles and data-structures useful in ...", "rank": 1, "display_url": "www.cs.rutgers.edu", "is_reddit_url": false}, {"title": "Quantum algorithms (CMSC 858Q, Spring 2025)", "url": "http://www.cs.umd.edu/class/spring2025/cmsc858Q/", "snippet": "This is an advanced graduate course on quantum algorithms for students with prior experience in quantum information.", "rank": 2, "display_url": "www.cs.umd.edu", "is_reddit_url": false}, {"title": "Data Stream Algorithms Lecture Notes | Dartmouth College", "url": "https://www.cs.dartmouth.edu/~ac/Teach/data-streams-lecnotes.pdf", "snippet": "Jul 1, 2025 ... I would like to thank the many Dartmouth students who took various editions of my course, as well as researchers around the world who told me ...", "rank": 3, "display_url": "www.cs.dartmouth.edu", "is_reddit_url": false}], "total_results": 423000000, "search_time": 1.2282848358154297}], "reddit_test_data": {"url_parsing": [{"url": "https://www.reddit.com/r/Python/comments/abc123/test_post/", "parsed": {"type": "post", "subreddit": "Python", "post_id": "abc123", "url": "https://www.reddit.com/r/Python/comments/abc123/test_post/"}}, {"url": "https://reddit.com/r/programming/", "parsed": {"type": "subreddit", "subreddit": "programming", "url": "https://reddit.com/r/programming/"}}, {"url": "https://www.reddit.com/user/testuser/", "parsed": {"type": "user", "username": "testuser", "url": "https://www.reddit.com/user/testuser/"}}], "commenters_extraction": {"test_comments": 2, "extracted_commenters": ["user2", "user1"]}}, "statistics": {"total_google_queries": 3, "total_google_results": 9, "google_api_stats": {"request_count": 4, "total_search_time": 6.990625858306885, "average_search_time": 1.7476564645767212, "search_method": "Google Custom Search API", "api_configured": true}, "reddit_api_stats": {"request_count": 0, "total_request_time": 0.0, "average_request_time": 0, "api_configured": true, "proxy_configured": true, "comments_per_post": 6, "user_history_posts": 10, "user_history_comments": 20}}, "success": true}