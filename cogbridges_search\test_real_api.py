#!/usr/bin/env python3
"""
CogBridges Search - 真实API测试
测试实际的Reddit API和Google搜索功能
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services import GoogleSearchService, RedditService, DataService
from utils.proxy_utils import test_proxy_connection, get_proxy_info
from config import config


async def test_real_apis():
    """测试真实的API连接和功能"""
    print("🚀 CogBridges Search - 真实API测试")
    print("=" * 60)
    
    # 1. 检查代理配置
    print("🔍 检查代理配置...")
    proxy_info = get_proxy_info()
    print(f"代理已配置: {proxy_info['proxy_configured']}")
    if proxy_info['proxy_configured']:
        print(f"代理设置: {proxy_info['proxy_settings']}")
        
        # 测试代理连接
        print("\n🌐 测试代理连接...")
        if test_proxy_connection():
            print("✅ 代理连接正常")
        else:
            print("❌ 代理连接失败")
            return False
    
    # 2. 检查配置
    print(f"\n📋 检查配置...")
    print(f"Reddit配置: {config.reddit_configured}")
    print(f"Google搜索配置: {config.google_search_configured}")
    
    # 3. 测试Google搜索服务
    print(f"\n🔍 测试Google搜索服务...")
    try:
        google_service = GoogleSearchService()
        print("✅ Google搜索服务初始化成功")
        
        # 测试连接
        print("🔗 测试Google搜索连接...")
        if google_service.test_connection():
            print("✅ Google搜索连接正常")
        else:
            print("❌ Google搜索连接失败")
            return False
        
        # 执行实际搜索
        print("🔍 执行实际搜索测试...")
        search_result = google_service.search("python programming tips", max_results=3)
        
        if search_result.success:
            print(f"✅ 搜索成功: 找到 {len(search_result.results)} 个结果")
            for i, result in enumerate(search_result.results, 1):
                print(f"  {i}. {result.title[:50]}...")
                print(f"     URL: {result.url}")
                print(f"     Reddit: {result.is_reddit_url}")
        else:
            print(f"❌ 搜索失败: {search_result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ Google搜索服务测试失败: {e}")
        return False
    
    # 4. 测试Reddit服务
    print(f"\n📱 测试Reddit服务...")
    try:
        reddit_service = RedditService()
        print("✅ Reddit服务初始化成功")
        
        if not reddit_service.configured:
            print("❌ Reddit API未正确配置")
            return False
        
        # 测试连接
        print("🔗 测试Reddit API连接...")
        if reddit_service.test_api_connection():
            print("✅ Reddit API连接正常")
        else:
            print("❌ Reddit API连接失败")
            return False
        
        # 测试获取帖子详情（使用一个已知的Reddit帖子）
        print("📝 测试获取Reddit帖子详情...")
        test_post_url = "https://www.reddit.com/r/Python/comments/1/"
        
        try:
            post_details = await reddit_service.get_post_details(test_post_url)
            if post_details:
                print(f"✅ 获取帖子成功: {post_details.title[:50]}...")
                print(f"   作者: {post_details.author}")
                print(f"   分数: {post_details.score}")
            else:
                print("⚠️ 未找到帖子（可能是测试URL无效）")
        except Exception as e:
            print(f"⚠️ 获取帖子测试跳过: {e}")
        
        # 测试获取评论
        print("💬 测试获取Reddit评论...")
        try:
            comments = await reddit_service.get_post_comments(test_post_url, limit=2)
            print(f"✅ 获取评论成功: 找到 {len(comments)} 条评论")
            for i, comment in enumerate(comments[:2], 1):
                print(f"  {i}. {comment.author}: {comment.body[:50]}...")
        except Exception as e:
            print(f"⚠️ 获取评论测试跳过: {e}")
            
    except Exception as e:
        print(f"❌ Reddit服务测试失败: {e}")
        return False
    
    # 5. 测试数据服务
    print(f"\n💾 测试数据服务...")
    try:
        data_service = DataService()
        print("✅ 数据服务初始化成功")
        
        # 测试保存搜索结果
        session_id = data_service.generate_session_id("test query")
        filepath = data_service.save_search_result(search_result, session_id)
        
        if os.path.exists(filepath):
            print(f"✅ 搜索结果保存成功: {filepath}")
        else:
            print("❌ 搜索结果保存失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据服务测试失败: {e}")
        return False
    
    # 6. 显示统计信息
    print(f"\n📊 服务统计信息:")
    
    google_stats = google_service.get_statistics()
    print(f"Google搜索统计:")
    for key, value in google_stats.items():
        print(f"  {key}: {value}")
    
    reddit_stats = reddit_service.get_statistics()
    print(f"Reddit服务统计:")
    for key, value in reddit_stats.items():
        print(f"  {key}: {value}")
    
    print(f"\n🎉 所有真实API测试通过！")
    return True


def main():
    """主函数"""
    try:
        success = asyncio.run(test_real_apis())
        if success:
            print("\n✅ 真实API测试完成，所有功能正常")
            sys.exit(0)
        else:
            print("\n❌ 真实API测试失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
