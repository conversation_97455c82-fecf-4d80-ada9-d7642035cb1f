{"posts_data": [{"post": {"id": "test123", "title": "Test Post", "author": "testuser", "score": 100, "subreddit": "test"}, "comments": [{"id": "1", "body": "Test comment 1", "author": "user1", "score": 10, "created_utc": 1640995200, "created_datetime": "2022-01-01T08:00:00", "parent_id": "t3_abc123", "subreddit": "test", "permalink": "/test/1", "url": "https://reddit.com/test/1", "is_submitter": false, "is_top_level": true, "gilded": 0, "replies_count": 0}, {"id": "2", "body": "Test comment 2", "author": "user2", "score": 5, "created_utc": 1640995300, "created_datetime": "2022-01-01T08:01:40", "parent_id": "t3_abc123", "subreddit": "test", "permalink": "/test/2", "url": "https://reddit.com/test/2", "is_submitter": false, "is_top_level": true, "gilded": 0, "replies_count": 0}]}], "user_histories": {}, "statistics": {"total_posts": 1, "total_commenters": 2, "processing_time": 1.5}, "success": true}