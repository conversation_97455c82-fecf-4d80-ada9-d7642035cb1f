2025-07-18 20:48:25 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:48:25 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:48:25 - services.google_search - INFO - 开始Google搜索: python programming
2025-07-18 20:48:25 - utils.proxy_utils - INFO - 未配置代理
2025-07-18 20:48:27 - services.google_search - WARNING - 未找到搜索结果容器
2025-07-18 20:48:27 - services.google_search - INFO - 搜索完成: 找到 0 个结果，耗时 2.02秒
2025-07-18 20:48:27 - api.Google - INFO - Google API调用成功，耗时: 2.04秒
2025-07-18 20:54:30 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:54:30 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:54:30 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:54:30 - services.google_search - INFO - 开始Google搜索: test query
2025-07-18 20:54:30 - services.google_search - ERROR - HTTP请求错误: Network error
2025-07-18 20:54:30 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 20:54:30 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:54:30 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:54:30 - services.google_search - INFO - 开始Google搜索: python tips inurl:comments
2025-07-18 20:54:30 - services.google_search - INFO - 搜索完成: 找到 1 个结果，耗时 0.00秒
2025-07-18 20:54:30 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 20:54:30 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:54:30 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:54:30 - services.google_search - INFO - 开始Google搜索: python programming
2025-07-18 20:54:30 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 20:54:30 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 20:54:30 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-18 20:54:30 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 20:54:30 - services.reddit_service - ERROR - 获取Reddit帖子失败: object Mock can't be used in 'await' expression
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - ERROR - Reddit API未配置
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit API连接测试成功
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - ERROR - Reddit API连接测试失败: API Error
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - services.google_search - WARNING - 获取搜索建议失败: Network error
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - services.google_search - WARNING - 未找到搜索结果容器
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:59:36 - services.google_search - INFO - 开始Google搜索: test query
2025-07-18 20:59:36 - services.google_search - ERROR - HTTP请求错误: Network error
2025-07-18 20:59:36 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:59:36 - services.google_search - INFO - 开始Google搜索: python tips inurl:comments
2025-07-18 20:59:36 - services.google_search - INFO - 搜索完成: 找到 1 个结果，耗时 0.00秒
2025-07-18 20:59:36 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:59:36 - services.google_search - INFO - 开始Google搜索: python programming
2025-07-18 20:59:36 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 20:59:36 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:59:36 - services.google_search - INFO - 开始Google搜索: test
2025-07-18 20:59:36 - services.google_search - INFO - 搜索完成: 找到 0 个结果，耗时 0.00秒
2025-07-18 20:59:36 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 20:59:36 - services.google_search - ERROR - Google搜索连接测试失败: 
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:59:36 - services.google_search - INFO - 开始Google搜索: python programming tips
2025-07-18 20:59:36 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 20:59:36 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-18 20:59:36 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 20:59:36 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-18 20:59:36 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - services.google_search - WARNING - 获取搜索建议失败: Network error
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - services.google_search - WARNING - 未找到搜索结果容器
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:01:29 - services.google_search - INFO - 开始Google搜索: test query
2025-07-18 21:01:29 - services.google_search - ERROR - HTTP请求错误: Network error
2025-07-18 21:01:29 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:01:29 - services.google_search - INFO - 开始Google搜索: python tips inurl:comments
2025-07-18 21:01:29 - services.google_search - INFO - 搜索完成: 找到 1 个结果，耗时 0.00秒
2025-07-18 21:01:29 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:01:29 - services.google_search - INFO - 开始Google搜索: python programming
2025-07-18 21:01:29 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 21:01:29 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:01:29 - services.google_search - INFO - 开始Google搜索: test
2025-07-18 21:01:29 - services.google_search - INFO - 搜索完成: 找到 1 个结果，耗时 0.00秒
2025-07-18 21:01:29 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索连接测试成功
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:01:29 - services.google_search - INFO - 开始Google搜索: python programming tips
2025-07-18 21:01:29 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 21:01:29 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-18 21:01:29 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 21:01:29 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-18 21:01:29 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - services.google_search - WARNING - 获取搜索建议失败: Network error
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - services.google_search - WARNING - 未找到搜索结果容器
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:14 - services.google_search - INFO - 开始Google搜索: test query
2025-07-18 21:30:14 - services.google_search - ERROR - HTTP请求错误: Network error
2025-07-18 21:30:14 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:14 - services.google_search - INFO - 开始Google搜索: python tips inurl:comments
2025-07-18 21:30:14 - services.google_search - INFO - 搜索完成: 找到 1 个结果，耗时 0.00秒
2025-07-18 21:30:14 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:14 - services.google_search - INFO - 开始Google搜索: python programming
2025-07-18 21:30:14 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 21:30:14 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:14 - services.google_search - INFO - 开始Google搜索: test
2025-07-18 21:30:14 - services.google_search - INFO - 搜索完成: 找到 1 个结果，耗时 0.00秒
2025-07-18 21:30:14 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索连接测试成功
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:14 - services.google_search - INFO - 开始Google搜索: python programming tips
2025-07-18 21:30:14 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 21:30:14 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 21:30:14 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-18 21:30:14 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 21:30:14 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-18 21:30:14 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 21:30:14 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - services.google_search - WARNING - 获取搜索建议失败: Network error
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - services.google_search - WARNING - 未找到搜索结果容器
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:49 - services.google_search - INFO - 开始Google搜索: test query
2025-07-18 21:30:49 - services.google_search - ERROR - HTTP请求错误: Network error
2025-07-18 21:30:49 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:49 - services.google_search - INFO - 开始Google搜索: python tips inurl:comments
2025-07-18 21:30:49 - services.google_search - INFO - 搜索完成: 找到 1 个结果，耗时 0.00秒
2025-07-18 21:30:49 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:49 - services.google_search - INFO - 开始Google搜索: python programming
2025-07-18 21:30:49 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 21:30:49 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:49 - services.google_search - INFO - 开始Google搜索: test
2025-07-18 21:30:49 - services.google_search - INFO - 搜索完成: 找到 1 个结果，耗时 0.00秒
2025-07-18 21:30:49 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索连接测试成功
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:49 - services.google_search - INFO - 开始Google搜索: python programming tips
2025-07-18 21:30:49 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 21:30:49 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 21:30:49 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-18 21:30:49 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 21:30:49 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-18 21:30:49 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 21:30:49 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
