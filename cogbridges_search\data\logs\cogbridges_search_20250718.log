2025-07-18 20:48:25 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:48:25 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:48:25 - services.google_search - INFO - 开始Google搜索: python programming
2025-07-18 20:48:25 - utils.proxy_utils - INFO - 未配置代理
2025-07-18 20:48:27 - services.google_search - WARNING - 未找到搜索结果容器
2025-07-18 20:48:27 - services.google_search - INFO - 搜索完成: 找到 0 个结果，耗时 2.02秒
2025-07-18 20:48:27 - api.Google - INFO - Google API调用成功，耗时: 2.04秒
2025-07-18 20:54:30 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:54:30 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:54:30 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:54:30 - services.google_search - INFO - 开始Google搜索: test query
2025-07-18 20:54:30 - services.google_search - ERROR - HTTP请求错误: Network error
2025-07-18 20:54:30 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 20:54:30 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:54:30 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:54:30 - services.google_search - INFO - 开始Google搜索: python tips inurl:comments
2025-07-18 20:54:30 - services.google_search - INFO - 搜索完成: 找到 1 个结果，耗时 0.00秒
2025-07-18 20:54:30 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 20:54:30 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:54:30 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:54:30 - services.google_search - INFO - 开始Google搜索: python programming
2025-07-18 20:54:30 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 20:54:30 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 20:54:30 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-18 20:54:30 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 20:54:30 - services.reddit_service - ERROR - 获取Reddit帖子失败: object Mock can't be used in 'await' expression
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - ERROR - Reddit API未配置
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit API连接测试成功
2025-07-18 20:54:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 20:54:30 - services.reddit_service - ERROR - Reddit API连接测试失败: API Error
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - services.google_search - WARNING - 获取搜索建议失败: Network error
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - services.google_search - WARNING - 未找到搜索结果容器
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:59:36 - services.google_search - INFO - 开始Google搜索: test query
2025-07-18 20:59:36 - services.google_search - ERROR - HTTP请求错误: Network error
2025-07-18 20:59:36 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:59:36 - services.google_search - INFO - 开始Google搜索: python tips inurl:comments
2025-07-18 20:59:36 - services.google_search - INFO - 搜索完成: 找到 1 个结果，耗时 0.00秒
2025-07-18 20:59:36 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:59:36 - services.google_search - INFO - 开始Google搜索: python programming
2025-07-18 20:59:36 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 20:59:36 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:59:36 - services.google_search - INFO - 开始Google搜索: test
2025-07-18 20:59:36 - services.google_search - INFO - 搜索完成: 找到 0 个结果，耗时 0.00秒
2025-07-18 20:59:36 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 20:59:36 - services.google_search - ERROR - Google搜索连接测试失败: 
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 20:59:36 - services.google_search - INFO - 开始Google搜索: python programming tips
2025-07-18 20:59:36 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 20:59:36 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 20:59:36 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 20:59:36 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-18 20:59:36 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 20:59:36 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-18 20:59:36 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - services.google_search - WARNING - 获取搜索建议失败: Network error
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - services.google_search - WARNING - 未找到搜索结果容器
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:01:29 - services.google_search - INFO - 开始Google搜索: test query
2025-07-18 21:01:29 - services.google_search - ERROR - HTTP请求错误: Network error
2025-07-18 21:01:29 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:01:29 - services.google_search - INFO - 开始Google搜索: python tips inurl:comments
2025-07-18 21:01:29 - services.google_search - INFO - 搜索完成: 找到 1 个结果，耗时 0.00秒
2025-07-18 21:01:29 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:01:29 - services.google_search - INFO - 开始Google搜索: python programming
2025-07-18 21:01:29 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 21:01:29 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:01:29 - services.google_search - INFO - 开始Google搜索: test
2025-07-18 21:01:29 - services.google_search - INFO - 搜索完成: 找到 1 个结果，耗时 0.00秒
2025-07-18 21:01:29 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索连接测试成功
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:01:29 - services.google_search - INFO - 开始Google搜索: python programming tips
2025-07-18 21:01:29 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 21:01:29 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:01:29 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:01:29 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-18 21:01:29 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 21:01:29 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-18 21:01:29 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - services.google_search - WARNING - 获取搜索建议失败: Network error
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - services.google_search - WARNING - 未找到搜索结果容器
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:14 - services.google_search - INFO - 开始Google搜索: test query
2025-07-18 21:30:14 - services.google_search - ERROR - HTTP请求错误: Network error
2025-07-18 21:30:14 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:14 - services.google_search - INFO - 开始Google搜索: python tips inurl:comments
2025-07-18 21:30:14 - services.google_search - INFO - 搜索完成: 找到 1 个结果，耗时 0.00秒
2025-07-18 21:30:14 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:14 - services.google_search - INFO - 开始Google搜索: python programming
2025-07-18 21:30:14 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 21:30:14 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:14 - services.google_search - INFO - 开始Google搜索: test
2025-07-18 21:30:14 - services.google_search - INFO - 搜索完成: 找到 1 个结果，耗时 0.00秒
2025-07-18 21:30:14 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索连接测试成功
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:14 - services.google_search - INFO - 开始Google搜索: python programming tips
2025-07-18 21:30:14 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 21:30:14 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:14 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:14 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 21:30:14 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-18 21:30:14 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 21:30:14 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-18 21:30:14 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 21:30:14 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - services.google_search - WARNING - 获取搜索建议失败: Network error
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - services.google_search - WARNING - 未找到搜索结果容器
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:49 - services.google_search - INFO - 开始Google搜索: test query
2025-07-18 21:30:49 - services.google_search - ERROR - HTTP请求错误: Network error
2025-07-18 21:30:49 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:49 - services.google_search - INFO - 开始Google搜索: python tips inurl:comments
2025-07-18 21:30:49 - services.google_search - INFO - 搜索完成: 找到 1 个结果，耗时 0.00秒
2025-07-18 21:30:49 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:49 - services.google_search - INFO - 开始Google搜索: python programming
2025-07-18 21:30:49 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 21:30:49 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:49 - services.google_search - INFO - 开始Google搜索: test
2025-07-18 21:30:49 - services.google_search - INFO - 搜索完成: 找到 1 个结果，耗时 0.00秒
2025-07-18 21:30:49 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索连接测试成功
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:30:49 - services.google_search - INFO - 开始Google搜索: python programming tips
2025-07-18 21:30:49 - services.google_search - INFO - 搜索完成: 找到 2 个结果，耗时 0.00秒
2025-07-18 21:30:49 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 21:30:49 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:30:49 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 21:30:49 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-18 21:30:49 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 21:30:49 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-18 21:30:49 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 21:30:49 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 21:56:44 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 21:56:47 - utils.proxy_utils - INFO - 代理连接测试成功
2025-07-18 21:56:47 - utils.proxy_utils - INFO - 当前IP: ************
2025-07-18 21:56:47 - services.google_search - INFO - Google搜索服务初始化成功（HTTP模式）
2025-07-18 21:56:47 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 21:56:47 - services.google_search - INFO - 开始Google搜索: test
2025-07-18 21:56:47 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 21:56:48 - services.google_search - WARNING - 未找到搜索结果容器
2025-07-18 21:56:48 - services.google_search - INFO - 搜索完成: 找到 0 个结果，耗时 1.34秒
2025-07-18 21:56:48 - api.Google - INFO - Google API调用成功，耗时: 1.34秒
2025-07-18 21:56:48 - services.google_search - ERROR - Google搜索连接测试失败: 
2025-07-18 21:57:24 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 21:58:29 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 21:58:40 - utils.proxy_utils - ERROR - 代理连接测试异常: HTTPSConnectionPool(host='httpbin.org', port=443): Read timed out. (read timeout=10)
2025-07-18 21:59:28 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 21:59:29 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 21:59:29 - services.reddit_service - INFO - Reddit API连接测试成功
2025-07-18 21:59:29 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-18 21:59:29 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 21:59:31 - services.reddit_service - ERROR - 获取Reddit帖子失败: received 404 HTTP response
2025-07-18 21:59:31 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-18 21:59:31 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 21:59:32 - services.reddit_service - ERROR - 获取Reddit帖子失败: received 404 HTTP response
2025-07-18 22:00:17 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:00:17 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 22:00:17 - services.reddit_service - INFO - Reddit API连接测试成功
2025-07-18 22:00:17 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-18 22:00:17 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 22:00:19 - services.reddit_service - ERROR - 获取Reddit帖子失败: received 404 HTTP response
2025-07-18 22:00:19 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-18 22:00:19 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 22:00:20 - services.reddit_service - ERROR - 获取Reddit帖子失败: received 404 HTTP response
2025-07-18 22:00:20 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-18 22:00:20 - services.data_service - INFO - Reddit数据已保存: D:\pycharmproject\CogBridges_v020\cogbridges_search\data\results\reddit_data_20250718_220020_3d2f487a.json
2025-07-18 22:21:20 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-18 22:21:20 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-18 22:21:20 - services.google_search_api - INFO - 开始Google Custom Search API搜索: test
2025-07-18 22:21:20 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:21:21 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.27秒
2025-07-18 22:21:21 - services.google_search_api - ERROR - Google搜索失败: SearchResult.__init__() got an unexpected keyword argument 'source'
2025-07-18 22:21:21 - api.Google Custom Search - ERROR - Google Custom Search API调用失败，耗时: 1.27秒，错误: SearchResult.__init__() got an unexpected keyword argument 'source'
2025-07-18 22:21:25 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-18 22:21:25 - services.google_search_api - INFO - 开始Google Custom Search API搜索: test
2025-07-18 22:21:25 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:21:27 - services.google_search_api - WARNING - 解析搜索结果项失败: GoogleSearchResult.__init__() got an unexpected keyword argument 'source'
2025-07-18 22:21:27 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 5220000000），耗时 2.00秒
2025-07-18 22:21:27 - services.google_search_api - ERROR - Google搜索失败: SearchResult.__init__() got an unexpected keyword argument 'source'
2025-07-18 22:21:27 - api.Google Custom Search - ERROR - Google Custom Search API调用失败，耗时: 2.00秒，错误: SearchResult.__init__() got an unexpected keyword argument 'source'
2025-07-18 22:21:31 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-18 22:21:31 - services.google_search_api - INFO - 开始Google Custom Search API搜索: test
2025-07-18 22:21:31 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:21:32 - services.google_search_api - WARNING - 解析搜索结果项失败: GoogleSearchResult.__init__() got an unexpected keyword argument 'source'
2025-07-18 22:21:32 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 5220000000），耗时 1.29秒
2025-07-18 22:21:32 - services.google_search_api - ERROR - Google搜索失败: SearchResult.__init__() got an unexpected keyword argument 'source'
2025-07-18 22:21:32 - api.Google Custom Search - ERROR - Google Custom Search API调用失败，耗时: 1.29秒，错误: SearchResult.__init__() got an unexpected keyword argument 'source'
2025-07-18 22:21:32 - services.google_search_api - ERROR - API连接测试失败: RetryError[<Future at 0x1e887c9a870 state=finished raised TypeError>]
2025-07-18 22:23:00 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-18 22:23:00 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-18 22:23:00 - services.google_search_api - INFO - 开始Google Custom Search API搜索: test
2025-07-18 22:23:00 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:23:02 - services.google_search_api - INFO - Google搜索完成: 找到 1 个结果（总计 5220000000），耗时 1.62秒
2025-07-18 22:23:02 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.62秒
2025-07-18 22:23:02 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-18 22:23:02 - services.google_search_api - INFO - 开始Google Custom Search API搜索: python programming tips
2025-07-18 22:23:02 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:23:03 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.30秒
2025-07-18 22:23:03 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.31秒
2025-07-18 22:23:03 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-18 22:23:03 - services.google_search_api - INFO - 开始Google Custom Search API搜索: machine learning tutorial
2025-07-18 22:23:03 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:23:05 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.60秒
2025-07-18 22:23:05 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.60秒
2025-07-18 22:23:05 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-18 22:23:05 - services.google_search_api - INFO - 开始Google Custom Search API搜索: web development best practices
2025-07-18 22:23:05 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:23:07 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 2.46秒
2025-07-18 22:23:07 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 2.46秒
2025-07-18 22:23:07 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:23:07 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 22:23:07 - services.reddit_service - INFO - Reddit API连接测试成功
2025-07-18 22:23:07 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-18 22:23:07 - services.data_service - ERROR - 保存搜索结果失败: 'SearchResult' object has no attribute 'to_dict'
2025-07-18 22:24:03 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:24:04 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:24:49 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:24:50 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:25:44 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:25:47 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:26:54 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-18 22:26:54 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-18 22:26:54 - services.google_search_api - INFO - 开始Google Custom Search API搜索: test
2025-07-18 22:26:54 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:26:55 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 0.92秒
2025-07-18 22:26:55 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 0.93秒
2025-07-18 22:27:22 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-18 22:27:22 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-18 22:27:22 - services.google_search_api - INFO - 开始Google Custom Search API搜索: lectures
2025-07-18 22:27:22 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:27:23 - services.google_search_api - INFO - Google搜索完成: 找到 1 个结果（总计 632000000），耗时 1.49秒
2025-07-18 22:27:23 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.49秒
2025-07-18 22:27:23 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-18 22:27:23 - services.google_search_api - INFO - 开始Google Custom Search API搜索: lectures
2025-07-18 22:27:23 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:27:25 - services.google_search_api - INFO - Google搜索完成: 找到 3 个结果（总计 632000000），耗时 1.55秒
2025-07-18 22:27:25 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.55秒
2025-07-18 22:27:25 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-18 22:27:25 - services.google_search_api - INFO - 开始Google Custom Search API搜索: assignments
2025-07-18 22:27:25 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:27:28 - services.google_search_api - INFO - Google搜索完成: 找到 3 个结果（总计 376000000），耗时 2.72秒
2025-07-18 22:27:28 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 2.72秒
2025-07-18 22:27:28 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-18 22:27:28 - services.google_search_api - INFO - 开始Google Custom Search API搜索: algorithms
2025-07-18 22:27:28 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:27:29 - services.google_search_api - INFO - Google搜索完成: 找到 3 个结果（总计 423000000），耗时 1.23秒
2025-07-18 22:27:29 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.23秒
2025-07-18 22:27:29 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:27:29 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-18 22:27:29 - services.reddit_service - INFO - Reddit API连接测试成功
2025-07-18 22:27:29 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-18 22:27:29 - services.data_service - INFO - 搜索结果已保存: D:\pycharmproject\CogBridges_v020\cogbridges_search\data\results\search_result_20250718_222729_f991660d.json
2025-07-18 22:27:29 - services.data_service - INFO - Reddit数据已保存: D:\pycharmproject\CogBridges_v020\cogbridges_search\data\results\reddit_data_20250718_222729_f991660d.json
2025-07-18 22:27:55 - services.google_search - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-18 22:27:55 - services.google_search - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-18 22:27:55 - services.google_search - WARNING - 获取搜索建议失败: Network error
2025-07-18 22:27:55 - services.google_search - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-18 22:27:55 - services.google_search - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-18 22:27:55 - services.google_search - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-18 22:27:55 - services.google_search - ERROR - 解析HTML搜索结果失败: name 'BeautifulSoup' is not defined
2025-07-18 22:27:55 - services.google_search - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-18 22:27:55 - services.google_search - ERROR - 解析HTML搜索结果失败: name 'BeautifulSoup' is not defined
2025-07-18 22:27:55 - services.google_search - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-18 22:27:55 - services.google_search - ERROR - 解析HTML搜索结果失败: name 'BeautifulSoup' is not defined
2025-07-18 22:27:55 - services.google_search - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-18 22:27:55 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 22:27:55 - services.google_search - INFO - 开始Google搜索: test query
2025-07-18 22:27:55 - services.google_search - ERROR - 搜索异常: 'GoogleSearchService' object has no attribute 'headers'
2025-07-18 22:27:55 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 22:27:55 - services.google_search - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-18 22:27:55 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 22:27:55 - services.google_search - INFO - 开始Google搜索: python tips inurl:comments
2025-07-18 22:27:55 - services.google_search - ERROR - 搜索异常: 'GoogleSearchService' object has no attribute 'headers'
2025-07-18 22:27:55 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 22:27:55 - services.google_search - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-18 22:27:55 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 22:27:55 - services.google_search - INFO - 开始Google搜索: python programming
2025-07-18 22:27:55 - services.google_search - ERROR - 搜索异常: 'GoogleSearchService' object has no attribute 'headers'
2025-07-18 22:27:55 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 22:27:55 - services.google_search - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-18 22:27:55 - services.google_search - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-18 22:27:55 - api.Google - INFO - 开始调用 Google API: GET search
2025-07-18 22:27:55 - services.google_search - INFO - 开始Google搜索: test
2025-07-18 22:27:55 - services.google_search - ERROR - 搜索异常: 'GoogleSearchService' object has no attribute 'headers'
2025-07-18 22:27:55 - api.Google - INFO - Google API调用成功，耗时: 0.00秒
2025-07-18 22:27:55 - services.google_search - ERROR - Google搜索连接测试失败: 搜索异常: 'GoogleSearchService' object has no attribute 'headers'
2025-07-18 22:27:55 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:27:57 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-18 22:27:57 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-18 22:27:57 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 22:27:57 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-18 22:27:57 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-18 22:27:57 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
