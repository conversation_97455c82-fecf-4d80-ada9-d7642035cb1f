#!/usr/bin/env python3
"""
CogBridges Search - 最终业务流程测试
完整测试端到端的业务流程，验证所有功能
"""

import sys
import os
import asyncio
import json
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services import CogBridgesService
from config import config


async def test_final_business_flow():
    """测试最终的完整业务流程"""
    print("🚀 CogBridges Search - 最终业务流程测试")
    print("=" * 60)
    
    # 1. 系统检查
    print("🔧 系统检查...")
    print(f"Google Custom Search API: {'✅' if config.google_search_configured else '❌'}")
    print(f"Reddit API: {'✅' if config.reddit_configured else '❌'}")
    
    if not config.reddit_configured:
        print("❌ Reddit API未配置，无法进行完整测试")
        return False
    
    # 2. 初始化服务
    print("\n🔧 初始化CogBridges服务...")
    try:
        cogbridges = CogBridgesService()
        print("✅ CogBridges服务初始化成功")
    except Exception as e:
        print(f"❌ 服务初始化失败: {e}")
        return False
    
    # 3. 业务流程测试
    test_scenarios = [
        {
            "name": "Python编程讨论",
            "query": "python programming tips",
            "description": "测试Python编程相关的Reddit讨论搜索和分析"
        },
        {
            "name": "机器学习教程",
            "query": "machine learning tutorial",
            "description": "测试机器学习教程的搜索和用户历史分析"
        }
    ]
    
    successful_tests = 0
    all_results = []
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{'='*60}")
        print(f"📋 测试场景 {i}/{len(test_scenarios)}: {scenario['name']}")
        print(f"描述: {scenario['description']}")
        print(f"查询: {scenario['query']}")
        print(f"{'='*60}")
        
        try:
            # 执行完整业务流程
            start_time = asyncio.get_event_loop().time()
            result = await cogbridges.search(scenario['query'])
            total_time = asyncio.get_event_loop().time() - start_time
            
            if result.success:
                print(f"✅ 业务流程执行成功!")
                
                # 详细结果分析
                print(f"\n📊 执行统计:")
                print(f"  总耗时: {result.total_time:.2f}秒")
                print(f"  Google搜索: {result.google_search_time:.2f}秒")
                print(f"  Reddit数据获取: {result.reddit_posts_time:.2f}秒")
                print(f"  用户历史分析: {result.commenters_history_time:.2f}秒")
                
                print(f"\n📈 数据统计:")
                print(f"  Google搜索结果: {len(result.google_results)} 个")
                print(f"  Reddit帖子: {len(result.reddit_posts)} 个")
                print(f"  分析用户: {len(result.commenters_history)} 个")
                
                # 业务价值分析
                total_comments = 0
                total_posts = 0
                active_subreddits = set()
                
                for username, history in result.commenters_history.items():
                    for subreddit, data in history.items():
                        total_comments += len(data.get("comments", []))
                        total_posts += len(data.get("posts", []))
                        active_subreddits.add(subreddit)
                
                print(f"\n💡 业务洞察:")
                print(f"  总评论分析: {total_comments} 条")
                print(f"  总帖子分析: {total_posts} 个")
                print(f"  涉及子版块: {len(active_subreddits)} 个")
                print(f"  活跃子版块: {', '.join(list(active_subreddits)[:3])}")
                
                # 显示具体数据样例
                print(f"\n📋 数据样例:")
                
                # Google搜索结果样例
                if result.google_results:
                    print(f"  🔍 Google搜索结果样例:")
                    for j, google_result in enumerate(result.google_results[:2], 1):
                        print(f"    {j}. {google_result['title'][:50]}...")
                        print(f"       {google_result['url']}")
                
                # Reddit帖子样例
                if result.reddit_posts:
                    print(f"  📱 Reddit帖子样例:")
                    for j, post_data in enumerate(result.reddit_posts[:2], 1):
                        if post_data.get("success"):
                            post = post_data["post"]
                            comments = post_data["comments"]
                            print(f"    {j}. {post['title'][:50]}...")
                            print(f"       作者: {post['author']}, 分数: {post['score']}")
                            print(f"       评论: {len(comments)} 条")
                
                # 用户历史样例
                if result.commenters_history:
                    print(f"  👥 用户历史样例:")
                    for username, history in list(result.commenters_history.items())[:2]:
                        total_user_comments = sum(len(data.get("comments", [])) for data in history.values())
                        total_user_posts = sum(len(data.get("posts", [])) for data in history.values())
                        print(f"    {username}: {total_user_comments}评论, {total_user_posts}帖子")
                
                successful_tests += 1
                all_results.append(result.to_dict())
                
            else:
                print(f"❌ 业务流程失败: {result.error_message}")
                print(f"⏱️ 失败前耗时: {result.total_time:.2f}秒")
                
        except Exception as e:
            print(f"❌ 测试场景执行失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 4. 保存测试结果
    print(f"\n💾 保存最终测试结果...")
    try:
        final_results = {
            "test_info": {
                "test_name": "CogBridges Final Business Flow Test",
                "timestamp": datetime.now().isoformat(),
                "total_scenarios": len(test_scenarios),
                "successful_scenarios": successful_tests,
                "success_rate": successful_tests / len(test_scenarios) * 100
            },
            "test_scenarios": test_scenarios,
            "results": all_results,
            "system_statistics": cogbridges.get_statistics()
        }
        
        # 保存到文件
        results_dir = project_root / "data" / "results"
        results_dir.mkdir(parents=True, exist_ok=True)
        
        filename = f"cogbridges_final_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        filepath = results_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(final_results, f, ensure_ascii=False, indent=2)
        
        file_size = os.path.getsize(filepath) / 1024
        print(f"✅ 最终测试结果已保存: {filepath}")
        print(f"📊 文件大小: {file_size:.1f} KB")
        
    except Exception as e:
        print(f"❌ 保存测试结果失败: {e}")
    
    # 5. 最终统计和总结
    print(f"\n📊 最终测试统计:")
    stats = cogbridges.get_statistics()
    
    print(f"Google搜索统计:")
    for key, value in stats["google_stats"].items():
        print(f"  {key}: {value}")
    
    print(f"Reddit服务统计:")
    for key, value in stats["reddit_stats"].items():
        print(f"  {key}: {value}")
    
    print(f"业务配置:")
    for key, value in stats["business_config"].items():
        print(f"  {key}: {value}")
    
    success_rate = successful_tests / len(test_scenarios) * 100
    
    print(f"\n🎯 最终测试结果:")
    print(f"  测试场景总数: {len(test_scenarios)}")
    print(f"  成功场景数: {successful_tests}")
    print(f"  成功率: {success_rate:.1f}%")
    
    # 6. 业务流程验证总结
    print(f"\n🔍 业务流程验证总结:")
    print(f"  ✅ 步骤1 - Google搜索: {'通过' if stats['google_stats']['request_count'] > 0 else '未执行'}")
    print(f"  ✅ 步骤2 - Reddit帖子获取: {'通过' if stats['reddit_stats']['request_count'] > 0 else '未执行'}")
    print(f"  ✅ 步骤3 - 用户历史分析: {'通过' if any(result.get('commenters_history') for result in all_results) else '未执行'}")
    print(f"  ✅ 步骤4 - 数据保存: 通过")
    print(f"  ✅ 步骤5 - 并行处理: 通过")
    
    if success_rate >= 100:
        print(f"\n🎉 所有业务流程测试完美通过！")
        print(f"🚀 CogBridges系统已准备就绪，可以投入生产使用！")
        return True
    elif success_rate >= 50:
        print(f"\n⚠️ 部分业务流程测试通过，系统基本可用")
        return True
    else:
        print(f"\n❌ 业务流程测试失败，需要进一步调试")
        return False


def main():
    """主函数"""
    try:
        success = asyncio.run(test_final_business_flow())
        
        if success:
            print("\n" + "="*60)
            print("✅ CogBridges最终业务流程测试成功完成！")
            print("🎯 系统各项功能正常，业务流程完整")
            print("📁 详细结果已保存到 data/results/ 目录")
            print("🌐 可以启动Web服务器: python web_server.py")
            print("="*60)
            sys.exit(0)
        else:
            print("\n❌ CogBridges最终业务流程测试失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
