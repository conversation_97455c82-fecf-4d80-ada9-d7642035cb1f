{"query": {"query": "complete system test", "timestamp": "2025-07-18T22:27:29.257868", "search_type": "reddit", "max_results": 10, "site_filter": ""}, "results": [{"title": "EE364b: Lecture Slides and Notes", "url": "http://stanford.edu/class/ee364b/lectures.html", "snippet": "These slides and notes will change and get updated throughout the quarter. Please check this page frequently.", "display_url": "stanford.edu", "rank": 1}, {"title": "EE363: Lecture Slides - Stanford", "url": "https://stanford.edu/class/ee363/lectures.html", "snippet": "These lecture slides are still changing, so don't print them yet. Linear quadratic regulator: Discrete-time finite horizon · LQR via Lagrange multipliers.", "display_url": "stanford.edu", "rank": 2}, {"title": "Stanford Engineering Everywhere | EE364A - Convex Optimization I", "url": "https://see.stanford.edu/Course/EE364A", "snippet": "Lecture Notes. Introduction, Lecture 1. Convex Sets, Lecture2. Convex Functions, Lectures 3-4. Convex Optimization Problems, Lectures 5-7. Duality, Lectures 8-7.", "display_url": "see.stanford.edu", "rank": 3}, {"title": "CIS194: Lecture notes and assignments", "url": "https://www.cis.upenn.edu/~cis1940/fall14/lectures.html", "snippet": "All homework assignments should emerge creatively from the Style guidelines. Homework is due at midnight at the end of the day listed.", "display_url": "www.cis.upenn.edu", "rank": 1}, {"title": "CS241: Assignments", "url": "https://student.cs.uwaterloo.ca/~cs241/assignments/", "snippet": "Assignments must be submitted using the Marmoset Submission and Testing Server. Read the section of the course outline on Marmoset usage before submitting.", "display_url": "student.cs.uwaterloo.ca", "rank": 2}, {"title": "CIS 670: Assignments and Grading", "url": "https://www.cis.upenn.edu/~sweirich/cis670/02/grading.htm", "snippet": "Grades for this course come from three components: homework, class participation and a semester project. There will be no exams. Homework policy. You should ...", "display_url": "www.cis.upenn.edu", "rank": 3}, {"title": "01:198:344 - Design and Analysis of Computer Algorithms", "url": "https://www.cs.rutgers.edu/academics/undergraduate/course-synopses/course-details/01-198-344-design-and-analysis-of-computer-algorithms", "snippet": "To study a variety of useful algorithms and analyze their complexity; by that experience to gain insight into principles and data-structures useful in ...", "display_url": "www.cs.rutgers.edu", "rank": 1}, {"title": "Quantum algorithms (CMSC 858Q, Spring 2025)", "url": "http://www.cs.umd.edu/class/spring2025/cmsc858Q/", "snippet": "This is an advanced graduate course on quantum algorithms for students with prior experience in quantum information.", "display_url": "www.cs.umd.edu", "rank": 2}, {"title": "Data Stream Algorithms Lecture Notes | Dartmouth College", "url": "https://www.cs.dartmouth.edu/~ac/Teach/data-streams-lecnotes.pdf", "snippet": "Jul 1, 2025 ... I would like to thank the many Dartmouth students who took various editions of my course, as well as researchers around the world who told me ...", "display_url": "www.cs.dartmouth.edu", "rank": 3}], "total_results": 1431000000, "search_time": 5.498348712921143, "timestamp": "2025-07-18T22:27:29.257868", "success": true, "error_message": ""}