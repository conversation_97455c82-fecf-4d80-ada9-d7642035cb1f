/* CogBridges - 现代极简搜索页面样式 */

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 颜色系统 */
    --primary-color: #4285f4;
    --primary-hover: #3367d6;
    --secondary-color: #f8f9fa;
    --text-primary: #202124;
    --text-secondary: #5f6368;
    --text-muted: #9aa0a6;
    --border-color: #dadce0;
    --border-hover: #c4c7c5;
    --background: #ffffff;
    --surface: #f8f9fa;
    --shadow: rgba(0, 0, 0, 0.1);
    --shadow-hover: rgba(0, 0, 0, 0.15);
    
    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    
    /* 字体系统 */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Sego<PERSON> UI', <PERSON><PERSON>, sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 24px;
    --font-size-2xl: 32px;
    --font-size-3xl: 48px;
    
    /* 圆角系统 */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 24px;
    --radius-full: 9999px;
    
    /* 动画 */
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #8ab4f8;
        --primary-hover: #aecbfa;
        --secondary-color: #303134;
        --text-primary: #e8eaed;
        --text-secondary: #9aa0a6;
        --text-muted: #5f6368;
        --border-color: #5f6368;
        --border-hover: #80868b;
        --background: #202124;
        --surface: #303134;
        --shadow: rgba(0, 0, 0, 0.3);
        --shadow-hover: rgba(0, 0, 0, 0.4);
    }
}

/* 基础样式 */
body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--background);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 容器布局 */
.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-2xl) 0;
    gap: var(--spacing-2xl);
}

/* Logo区域 */
.logo-section {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.logo {
    font-size: var(--font-size-3xl);
    font-weight: 300;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    letter-spacing: -0.02em;
}

.tagline {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    font-weight: 400;
}

/* 搜索区域 */
.search-section {
    width: 100%;
    max-width: 584px;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.search-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.search-container {
    position: relative;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-md) var(--spacing-lg);
    transition: var(--transition);
    box-shadow: 0 2px 8px var(--shadow);
}

.search-input-wrapper:hover {
    border-color: var(--border-hover);
    box-shadow: 0 4px 12px var(--shadow-hover);
}

.search-input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px var(--shadow-hover);
}

.search-icon {
    width: 20px;
    height: 20px;
    color: var(--text-muted);
    margin-right: var(--spacing-md);
    flex-shrink: 0;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: var(--font-size-base);
    color: var(--text-primary);
    font-family: inherit;
}

.search-input::placeholder {
    color: var(--text-muted);
}

.clear-button {
    background: none;
    border: none;
    padding: var(--spacing-xs);
    cursor: pointer;
    color: var(--text-muted);
    border-radius: var(--radius-sm);
    transition: var(--transition);
    margin-left: var(--spacing-sm);
}

.clear-button:hover {
    background: var(--surface);
    color: var(--text-secondary);
}

.clear-button svg {
    width: 16px;
    height: 16px;
}

/* 搜索建议 */
.suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: 0 8px 24px var(--shadow);
    z-index: 1000;
    margin-top: var(--spacing-xs);
    max-height: 300px;
    overflow-y: auto;
}

.suggestions-list {
    padding: var(--spacing-sm) 0;
}

.suggestion-item {
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.suggestion-item:hover {
    background: var(--surface);
}

.suggestion-item.active {
    background: var(--surface);
}

/* 搜索按钮 */
.search-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.search-btn {
    padding: var(--spacing-md) var(--spacing-xl);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--surface);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-family: inherit;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-height: 44px;
}

.search-btn:hover {
    border-color: var(--border-hover);
    box-shadow: 0 2px 8px var(--shadow);
}

.search-btn.primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.search-btn.primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

.search-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 搜索选项 */
.search-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: center;
}

.option-group {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
    justify-content: center;
}

.option-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    user-select: none;
}

.option-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    position: relative;
    transition: var(--transition);
}

.option-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.option-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 3px;
    top: 0px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.advanced-options {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
    justify-content: center;
    padding: var(--spacing-md);
    background: var(--surface);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.option-row {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.option-row select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--background);
    color: var(--text-primary);
    font-family: inherit;
    font-size: var(--font-size-sm);
}

.advanced-toggle {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: var(--font-size-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.advanced-toggle:hover {
    background: var(--surface);
}

.toggle-icon {
    width: 16px;
    height: 16px;
    transition: var(--transition);
}

.advanced-toggle.active .toggle-icon {
    transform: rotate(180deg);
}

/* 快速链接 */
.quick-links {
    text-align: center;
    margin-top: var(--spacing-xl);
}

.quick-links h3 {
    font-size: var(--font-size-lg);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    max-width: 600px;
    margin: 0 auto;
}

.quick-link {
    padding: var(--spacing-md);
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    text-decoration: none;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: var(--transition);
    display: block;
}

.quick-link:hover {
    background: var(--background);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow);
}

/* 页脚 */
.footer {
    margin-top: auto;
    padding: var(--spacing-xl) 0;
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    text-align: center;
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
    justify-content: center;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-info {
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
}

.modal-content {
    background: var(--background);
    border-radius: var(--radius-lg);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 500;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    cursor: pointer;
    color: var(--text-muted);
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--surface);
    color: var(--text-secondary);
}

.modal-close svg {
    width: 20px;
    height: 20px;
}

.modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
    flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }
    
    .main-content {
        padding: var(--spacing-lg) 0;
        gap: var(--spacing-lg);
    }
    
    .logo {
        font-size: var(--font-size-2xl);
    }
    
    .tagline {
        font-size: var(--font-size-base);
    }
    
    .search-section {
        max-width: 100%;
    }
    
    .search-buttons {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-btn {
        justify-content: center;
    }
    
    .option-group {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .advanced-options {
        flex-direction: column;
        align-items: center;
    }
    
    .links-grid {
        grid-template-columns: 1fr;
        max-width: 300px;
    }
    
    .footer-links {
        flex-direction: column;
        gap: var(--spacing-md);
    }
}
