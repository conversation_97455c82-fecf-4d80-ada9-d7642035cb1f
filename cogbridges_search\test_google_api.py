#!/usr/bin/env python3
"""
测试Google Custom Search API配置
"""

import sys
import json
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config
from utils.proxy_utils import get_proxy_session


def test_google_api_direct():
    """直接测试Google Custom Search API"""
    print("🔍 直接测试Google Custom Search API...")
    
    # 显示配置信息
    print(f"API Key: {config.GOOGLE_SEARCH_API_KEY[:10]}...")
    print(f"Engine ID: {config.GOOGLE_SEARCH_ENGINE_ID}")
    
    # 构建API请求
    api_url = "https://www.googleapis.com/customsearch/v1"
    params = {
        'key': config.GOOGLE_SEARCH_API_KEY,
        'cx': config.GOOGLE_SEARCH_ENGINE_ID,
        'q': 'lectures',  # 搜索这个CS课程搜索引擎能找到的内容
        'num': 3
    }
    
    try:
        session = get_proxy_session()
        response = session.get(api_url, params=params, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"API响应成功!")
            
            # 显示搜索信息
            search_info = data.get('searchInformation', {})
            print(f"总结果数: {search_info.get('totalResults', 0)}")
            print(f"搜索时间: {search_info.get('searchTime', 0)}秒")
            
            # 显示结果
            items = data.get('items', [])
            print(f"返回结果数: {len(items)}")
            
            for i, item in enumerate(items, 1):
                print(f"\n结果 {i}:")
                print(f"  标题: {item.get('title', '')}")
                print(f"  URL: {item.get('link', '')}")
                print(f"  摘要: {item.get('snippet', '')[:100]}...")
                print(f"  显示链接: {item.get('displayLink', '')}")
            
            # 保存完整响应到文件
            response_file = project_root / "google_api_response.json"
            with open(response_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"\n完整API响应已保存到: {response_file}")
            
        else:
            print(f"API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"API测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_reddit_search():
    """测试Reddit特定搜索"""
    print("\n🔍 测试Reddit特定搜索...")
    
    api_url = "https://www.googleapis.com/customsearch/v1"
    params = {
        'key': config.GOOGLE_SEARCH_API_KEY,
        'cx': config.GOOGLE_SEARCH_ENGINE_ID,
        'q': 'assignments',  # 搜索这个CS课程搜索引擎能找到的内容
        'num': 5
    }
    
    try:
        session = get_proxy_session()
        response = session.get(api_url, params=params, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            search_info = data.get('searchInformation', {})
            print(f"Reddit搜索总结果数: {search_info.get('totalResults', 0)}")
            
            items = data.get('items', [])
            print(f"Reddit搜索返回结果数: {len(items)}")
            
            for i, item in enumerate(items, 1):
                url = item.get('link', '')
                is_reddit = 'reddit.com' in url.lower()
                print(f"\n结果 {i} (Reddit: {is_reddit}):")
                print(f"  标题: {item.get('title', '')}")
                print(f"  URL: {url}")
                
        else:
            print(f"Reddit搜索失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"Reddit搜索测试失败: {e}")


def main():
    """主函数"""
    print("🚀 Google Custom Search API 配置测试")
    print("=" * 50)
    
    # 检查配置
    if not config.google_search_configured:
        print("❌ Google Custom Search API未配置")
        return
    
    print("✅ Google Custom Search API已配置")
    
    # 测试基本API
    test_google_api_direct()
    
    # 测试Reddit搜索
    test_reddit_search()


if __name__ == "__main__":
    main()
