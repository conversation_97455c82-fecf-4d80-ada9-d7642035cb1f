#!/usr/bin/env python3
"""
测试Google搜索服务
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入服务
from services.google_search import GoogleSearchService

def test_google_search():
    """测试Google搜索功能"""
    print("🔍 测试Google搜索服务")
    print("=" * 50)
    
    try:
        # 创建搜索服务
        service = GoogleSearchService()
        print("✅ Google搜索服务初始化成功")
        
        # 执行测试搜索
        print("\n🔍 执行测试搜索...")
        query = "python programming"
        result = service.search(query, max_results=3, site_filter="site:reddit.com")
        
        print(f"查询: {query}")
        print(f"成功: {result.success}")
        print(f"结果数量: {len(result.results)}")
        print(f"搜索时间: {result.search_time:.2f}秒")
        
        if result.success and result.results:
            print("\n📋 搜索结果:")
            for i, item in enumerate(result.results, 1):
                print(f"\n{i}. {item.title}")
                print(f"   URL: {item.url}")
                print(f"   Reddit URL: {item.is_reddit_url}")
                print(f"   摘要: {item.snippet[:100]}...")
        elif not result.success:
            print(f"❌ 搜索失败: {result.error_message}")
        else:
            print("⚠️ 未找到搜索结果")
        
        # 显示统计信息
        print("\n📊 服务统计:")
        stats = service.get_statistics()
        for key, value in stats.items():
            print(f"  {key}: {value}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_google_search()
