{"test_info": {"test_name": "CogBridges Real Reddit Business Flow Test", "timestamp": "2025-07-19T14:52:38.705961", "session_id": "20250719_145230_837be873", "total_time": 7.9689999999827705}, "mock_google_results": [{"title": "Python Programming Tips - r/Python", "url": "https://www.reddit.com/r/Python/comments/1234567/python_programming_tips/", "snippet": "Great tips for Python programming from the community", "rank": 1}, {"title": "Learn Python - r/learnpython", "url": "https://www.reddit.com/r/learnpython/comments/7890123/best_way_to_learn_python/", "snippet": "Discussion about the best ways to learn Python programming", "rank": 2}, {"title": "Python Tutorial - r/programming", "url": "https://www.reddit.com/r/programming/comments/4567890/comprehensive_python_tutorial/", "snippet": "A comprehensive Python tutorial for beginners", "rank": 3}], "reddit_posts": [{"success": true, "google_result": {"title": "Python Programming Tips - r/Python", "url": "https://www.reddit.com/r/Python/comments/1234567/python_programming_tips/", "snippet": "Great tips for Python programming from the community", "rank": 1}, "post": {"id": "1234567", "title": "My boyfriend (M19) suddenly became extremely distant with me (F18) and I'm not sure why", "author": "[deleted]", "score": 2, "num_comments": 8, "subreddit": "relationships", "url": "https://www.reddit.com/r/relationships/comments/1234567/my_boyfriend_m19_suddenly_became_extremely/", "created_utc": 1679874346.0, "selftext": "We haven't been dating for that long, we've only been seeing each other for a couple months and been official for one month. Everything has been pretty great with him but for some reason he just suddenly started acting kinda strange on our most recent date on friday. He seemed kinda quiet when I came over, and he mentioned the only thing he's had today was a monster. We went and saw a movie in the theater and when it was finished, he said he wanted to go back to his dorm (instead of to my apartment like we planned). I wasn't too surprised though because we live kinda far, it was late, and he had work the next day. But he basically just said goodbye and hugged me when we got there and didn't kiss me like usual. I thought it was kinda weird but I wasn't worried yet.\n\nThe next morning I asked if he ate anything for dinner and he said yeah pizza. Then the next message I sent he left me on seen. I decided to give some space and didn't send anything until later that night which he barely reacted to. I haven't really sent anything and it's Sunday and I haven't heard anything from him. He's usually very affectionate and responds relatively quickly and often sends me stuff. At this point I'm kinda concerned. I've been trying not to overthink and I know rationally it most likely has nothing to do with me, but I'm still worried that he's just not doing well. I'm autistic and I don't pick up on body language/social cues very easily but I think something is definitely off. I don't know if I should keep giving him space or if I should reach out and ask what's wrong\n\nT<PERSON>;DR my boyfriend suddenly became extremely distant and I don't know what to do"}, "comments": [{"id": "jdt3ho1", "author": "[deleted]", "body": "[removed]", "score": 1, "created_utc": 1679874445.0}, {"id": "jdtbt1h", "author": "[deleted]", "body": "Some men are bad at talking about their feelings, my bf does the exact same thing and it’s always bc something happened and I have to kinda get it out of him. Just reach out!", "score": 1, "created_utc": 1679878423.0}, {"id": "jlm316a", "author": "the_genius324", "body": "I thought I would see someone here", "score": 1, "created_utc": 1685047060.0}], "commenters": ["the_genius324"]}], "reddit_posts_time": 5.441122055053711, "commenters_history": {"the_genius324": {"relationships": {"comments": [], "posts": []}}}, "commenters_history_time": 2.5215580463409424, "statistics": {"google_stats": {"request_count": 0, "total_search_time": 0.0, "average_search_time": 0, "search_method": "Google Custom Search API", "api_configured": true}, "reddit_stats": {"request_count": 2, "total_request_time": 5.236572265625, "average_request_time": 2.6182861328125, "api_configured": true, "proxy_configured": true, "comments_per_post": 6, "user_history_posts": 10, "user_history_comments": 20}, "business_config": {"max_search_results": 5, "max_comments_per_post": 6, "max_user_comments": 20, "max_user_posts": 10}}, "success": true}