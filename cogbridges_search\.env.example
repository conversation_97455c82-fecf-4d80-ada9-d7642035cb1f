# CogBridges Search - 环境变量配置示例
# 复制此文件为 .env 并填入实际的API密钥和配置

# =============================================================================
# Google搜索配置（HTTP模式）
# =============================================================================
# 使用HTTP请求方式搜索Google，无需API密钥
# 注意：请遵守Google的使用条款，避免过于频繁的请求

# =============================================================================
# Reddit API 配置
# =============================================================================
# 在 Reddit 创建应用获取凭据
# https://www.reddit.com/prefs/apps
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here
REDDIT_USER_AGENT=CogBridges-Search/1.0 by YourUsername

# =============================================================================
# 代理配置 (可选)
# =============================================================================
# Clash 代理配置 - 用于访问 Reddit API
HTTP_PROXY=http://127.0.0.1:7890
HTTPS_PROXY=http://127.0.0.1:7890

# 如果不使用代理，请注释掉上面两行
# HTTP_PROXY=
# HTTPS_PROXY=

# =============================================================================
# 应用配置
# =============================================================================
# Flask 应用配置
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your_secret_key_here

# 服务器配置
HOST=127.0.0.1
PORT=5000

# =============================================================================
# 搜索参数配置
# =============================================================================
# Google 搜索结果数量 (1-10)
GOOGLE_SEARCH_RESULTS_COUNT=5

# Reddit 评论获取数量
REDDIT_TOP_COMMENTS_COUNT=6

# 用户历史数据获取数量
USER_HISTORY_COMMENTS_COUNT=20
USER_HISTORY_POSTS_COUNT=10

# =============================================================================
# 数据存储配置
# =============================================================================
# 数据存储目录
DATA_DIR=data
LOGS_DIR=data/logs
RESULTS_DIR=data/results

# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# 是否保存详细日志
SAVE_DETAILED_LOGS=True

# =============================================================================
# 性能配置
# =============================================================================
# 并发请求数量限制
MAX_CONCURRENT_REQUESTS=10

# 请求超时时间 (秒)
REQUEST_TIMEOUT=30

# 重试次数
MAX_RETRIES=3

# 请求间隔 (秒)
REQUEST_DELAY=1

# =============================================================================
# 缓存配置
# =============================================================================
# 是否启用缓存
ENABLE_CACHE=True

# 缓存过期时间 (秒)
CACHE_EXPIRE_TIME=3600

# =============================================================================
# 安全配置
# =============================================================================
# 是否启用CORS
ENABLE_CORS=True

# 允许的域名 (用逗号分隔)
ALLOWED_ORIGINS=http://localhost:5000,http://127.0.0.1:5000

# =============================================================================
# 开发配置
# =============================================================================
# 是否启用调试模式
DEBUG_MODE=True

# 是否启用性能监控
ENABLE_PROFILING=False

# 测试模式 (使用模拟数据)
TEST_MODE=False
