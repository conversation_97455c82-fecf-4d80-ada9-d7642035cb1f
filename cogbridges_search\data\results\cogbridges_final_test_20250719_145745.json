{"test_info": {"test_name": "CogBridges Final Business Flow Test", "timestamp": "2025-07-19T14:57:45.584373", "total_scenarios": 2, "successful_scenarios": 0, "success_rate": 0.0}, "test_scenarios": [{"name": "Python编程讨论", "query": "python programming tips", "description": "测试Python编程相关的Reddit讨论搜索和分析"}, {"name": "机器学习教程", "query": "machine learning tutorial", "description": "测试机器学习教程的搜索和用户历史分析"}], "results": [], "system_statistics": {"google_stats": {"request_count": 2, "total_search_time": 2.517277717590332, "average_search_time": 1.258638858795166, "search_method": "Google Custom Search API", "api_configured": true}, "reddit_stats": {"request_count": 0, "total_request_time": 0.0, "average_request_time": 0, "api_configured": true, "proxy_configured": true, "comments_per_post": 6, "user_history_posts": 10, "user_history_comments": 20}, "business_config": {"max_search_results": 5, "max_comments_per_post": 6, "max_user_comments": 20, "max_user_posts": 10}}}