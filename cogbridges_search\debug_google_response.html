<!DOCTYPE html><html lang="en"><head><title>Google Search</title><style>body{background-color:#fff}</style></head><body><noscript><style>table,div,span,p{display:none}</style><meta content="0;url=/httpservice/retry/enablejs?sei=xFJ6aJ-vINzvkPIP_fiouQc" http-equiv="refresh"><div style="display:block">Please click <a href="/httpservice/retry/enablejs?sei=xFJ6aJ-vINzvkPIP_fiouQc">here</a> if you are not redirected within a few seconds.</div></noscript><script nonce="GNUl1Yy1fURJHDKhr2nUrw">(function(){var sctm=false;(function(){sctm&&google.tick("load","pbsst");}).call(this);})();</script><script nonce="GNUl1Yy1fURJHDKhr2nUrw">//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjogMywic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiICJdLCJuYW1lcyI6WyJjbG9zdXJlRHluYW1pY0J1dHRvbiJdLCJtYXBwaW5ncyI6IkFBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUEifQ==
(function(){var Z=function(g,J,q,y,n,L,P,t,V,E){for(V=0;V!=65;){if(V==79)return E;if(V==21){if((P=(t=y,W.trustedTypes))&&P.createPolicy){try{t=P.createPolicy(n,{createHTML:A,createScript:A,createScriptURL:A})}catch(w){if(W.console)W.console[L](w.message)}E=t}else E=t;V=79}else V==g?(E=y,V=30):V==0?V=73:V==73?V=(q&J)==q?g:30:V==30&&(V=(q^23)&6?79:21)}},A=function(g){return Z.call(this,75,44,8,g)},W=this||self;(0,eval)(function(g,J){return(J=Z(75,44,6,null,"ks","error"))&&g.eval(J.createScript("1"))===1?function(q){if({})return J.createScript(q)}:function(q){return""+q}}(W)(Array(Math.random()*7824|0).join("\n")+['//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjogMywic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiICJdLCJuYW1lcyI6WyJjbG9zdXJlRHluYW1pY0J1dHRvbiJdLCJtYXBwaW5ncyI6IkFBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUEifQ==',
'(function(){/*',
'',
' Copyright Google LLC',
' SPDX-License-Identifier: Apache-2.0',
'*/',
'var JR=function(g,V,q,J,P,t,W,y,L){for(y=2;y!=75;)if(y==24)L=!!(P=q.eP,-~(P|J)-(~P&J)+(~P|J))&&!!(q.WV&J),y=86;else if(y==11){for(t in W=((Array.prototype.forEach.call(k(47,(P={},g),J),function(n){P[n]=true}),Array.prototype.forEach).call(q,function(n){P[n]=true}),""),P)W+=W.length>0?" "+t:t;k(32,"class",J,W),y=91}else if(y==38)y=(V&41)==V?24:86;else if(y==2)y=38;else{if(y==91)return L;y==86?y=(V+5^23)<V&&V-6<<1>=V?87:91:y==87?y=J.classList?22:11:y==22&&(Array.prototype.forEach.call(q,function(n,E,A){for(A=0;A!=65;)A==30?A=q$(6,1,0,3,n,J)?65:79:A==73?(J.classList.add(n),A=65):A==79?(E=gN(56,19,g,"class",J),k(38,"class",J,E+(E.length>0?" "+n:n)),A=65):A==0&&(A=J.classList?73:30)}),y=91)}},k=function(g,V,q,J,P,t){for(t=9;t!=18;)if(t==79)t=g>>1<10&&g+6>=6?5:4;else if(t==54)typeof q.className=="string"?q.className=J:q.setAttribute&&q.setAttribute(V,J),t=79;else{if(t==4)return P;t==15?(P=q.classList?q.classList:gN(56,18,V,"class",q).match(/\\S+/g)||[],t=40):t==5?(q.vV=true,q.listener=V,q.proxy=V,q.src=V,q.on=V,t=4):t==9?t=17:t==17?t=(g+5&63)>=g&&(g-5|41)<g?15:40:t==40&&(t=(g&110)==g?54:79)}},XD=function(g,V,q,J,P,t,W,y,L,n,E,A,Z,w){{Z=68;while(Z!=37)if(Z==54)Z=y.Cn==q?45:64;else if(Z==32)vL(V,3,t.C,J),Z=64;else if(Z==92)Z=(g&101)==g?53:64;else if(Z==10)k(11,V,J),Z=64;else if(Z==45)y.src=V,t[wN]=V,Z=64;else if(Z==64)Z=g+7&14?2:19;else if(Z==40)W=J.type,P=J.proxy,t.removeEventListener?t.removeEventListener(W,P,J.capture):t.detachEvent?t.detachEvent(tR(3,"on",W),P):t.addListener&&t.removeListener&&t.removeListener(P),z3--,y=LG(4,18,t),Z=96;else if(Z==96)Z=y?43:10;else if(Z==53)Z=typeof J!=="number"&&J&&!J.vV?88:64;else{if(Z==2)return w;if(Z==58)Z=t&&t[rN]?32:40;else if(Z==25)Z=g-8<19&&g-6>=6?11:92;else if(Z==68)Z=25;else if(Z==43)vL(V,17,y,J),Z=54;else if(Z==88)t=J.src,Z=58;else if(Z==11)w=this.n===0?0:Math.sqrt(this.F6/this.n),Z=92;else if(Z==19){if(E=J.C.h[String(P)]){for(W=(E=E.concat(),A=true,V);W<E.length;++W)(L=E[W])&&!L.vV&&L.capture==q&&(n=L.on||L.src,y=L.listener,L.zm&&vL(null,16,J.C,L),A=y.call(n,t)!==false&&A);w=A&&!t.defaultPrevented}else w=true;Z=2}}}},jB=function(g,V,q,J,P,t,W,y,L){for(L=g;L!=84;)if(L==36)L=q?45:91;else{if(L==91)throw Error("Invalid class name "+q);if(L==10)throw Error("Invalid decorator function "+J);if(L==96)y=Object.prototype.hasOwnProperty.call(q,Z4)&&q[Z4]||(q[Z4]=++na),L=9;else if(L==g)L=1;else if(L==95)L=(V^35)&7?9:96;else if(L==20){a:{for(W in t)if(P.call(void 0,t[W],W,t)){y=J;break a}y=q}L=95}else if(L==1)L=(V-8|43)<V&&V-3<<2>=V?20:95;else{if(L==68)return y;L==9?L=(V&45)==V?36:68:L==45&&(L=typeof J!=="function"?10:68)}}},vL=function(g,V,q,J,P,t,W){{W=14;while(W!=84)if(W==67)this.n++,W=18;else if(W==95)W=V>>2&11?9:49;else if(W==77)q=Math.floor(Math.random()*this.n),q<50&&(this.L[q]=g),W=60;else if(W==9)W=(V+5&67)>=V&&(V+1&30)<V?70:83;else if(W==4)W=(V>>2&11)==3?67:60;else if(W==56)W=q.h[P].length==0?55:9;else if(W==18)W=this.L.length<50?0:77;else if(W==0)this.L.push(g),W=60;else if(W==5)W=P in q.h&&Dq(1,q.h[P],J,38)?37:9;else if(W==70)J=g,t=function(){return J<q.length?{done:false,value:q[J++]}:{done:true}},W=83;else if(W==55)delete q.h[P],q.Cn--,W=9;else if(W==63){a:if(typeof q==="string")t=typeof J!=="string"||J.length!=g?-1:q.indexOf(J,0);else{for(P=0;P<q.length;P++)if(P in q&&q[P]===J){t=P;break a}t=-1}W=4}else if(W==76)W=(V+2&23)==3?79:95;else if(W==14)W=76;else if(W==49)P=J.type,W=5;else if(W==79)RR.call(this,g,q||M$.PV(),J),W=95;else{if(W==60)return t;W==83?W=V-4&15?4:63:W==37&&(k(5,g,J),W=56)}}},CG=function(g,V,q,J,P,t,W,y,L,n){{n=40;while(n!=26)if(n==1)n=((P|5)&8)<2&&(P|3)>>3>=1?34:55;else if(n==40)n=29;else if(n==46)n=J?68:33;else if(n==14)n=86;else{if(n==55)return L;n==33?(t=void 0,n=17):n==2?(y+=String.fromCharCode.apply(null,q.slice(W,W+8192)),n=16):n==68?(W=0,y="",n=14):n==34?(q.classList?Array.prototype.forEach.call(J,function(E){bY(45,0," ","string",7,1,E,q)}):k(34,"class",q,Array.prototype.filter.call(k(g,"string",q),function(E){return!(vL(1,36,J,E)>=0)}).join(" ")),n=55):n==17?(L=t,n=1):n==86?n=W<q.length?2:94:n==4?(J=window.btoa,n=46):n==29?n=(P^6)>>3==1?4:1:n==16?(W+=8192,n=86):n==94&&(t=J(y).replace(/\\+/g,"-").replace(/\\//g,V).replace(/=/g,""),n=17)}}},K=function(g,V,q,J,P,t,W,y){if((g-6&(((g|9)&7)>=1&&((g^51)&4)<4&&(V.I?y=xB(V.X,V):(P=iY(true,V,8),(P|128)-(P&-129)-(~P&128)&&(P=(P&128)+2*~(P&128)-~P-(P|-129),J=iY(true,V,2),P=(q=P<<2,~q-3*~J+2*(q&~J)+2*(q|~J))),y=P)),6))>=3&&g-4<21)if(P=kB("null","object",J)==="array"?J:[J],this.A)V(this.A);else try{t=!this.P.length,W=[],S(0,47,[oR,W,P],this),S(0,42,[SB,V,W],this),q&&!t||T(17,254,q,this,true)}catch(L){T3(35,10,2048,L,this),V(this.A)}(g+1&62)>=g&&(g+6^12)<g&&(J=KG(8,true,q),(J|128)-~(J&128)+~(J|128)&&(J=(P=J&V,t=KG(8,true,q)<<7,-~(P|t)+(P&~t)+(~P^t)+(~P&t))),y=J);while(-0===0){return y;if({})break}},T=function(g,V,q,J,P,t,W,y,L,n,E,A,Z,w){for(w=48;w!=42;)if(w==94)w=y&&y[rN]?30:49;else if(w==27)A=V,w=95;else if(w==70)T(9,0,null,J[A],P,t,W,y),w=28;else if(w==57)w=Array.isArray(J)?27:89;else if(w==49)w=y?82:51;else if(w==6){if(J.P.length){(J.Y7=(J.uS&&":TQR:TQR:"(),q),J).uS=true;try{y=J.G(),J.wx=y,J.S8=0,J.O5=0,J.AN=y,L=Dq(3,254,2048,16,true,J,q),W=P?0:10,t=J.G()-J.wx,J.yk+=t,J.a8&&J.a8(t-J.u,J.F,J.B,J.O5),J.B=false,J.F=false,J.u=0,t<W||J.lN--<=0||(t=Math.floor(t),J.bS.push(t<=V?t:254))}finally{J.uS=false}Z=L}w=1}else if(w==48)w=5;else if(w==28)A++,w=50;else if(w==82)L=LG(4,17,y),w=16;else if(w==5)w=(g<<2&10)<3&&(g+7&15)>=8?6:1;else if(w==15)(n=L.Vk(t,P,E,J))&&XD(32,null,0,n),w=51;else if(w==95)w=50;else if(w==30)y.C.remove(String(J),P,E,t),w=51;else if(w==50)w=A<J.length?70:51;else if(w==1)w=(g|5)>>4?51:57;else{if(w==38)return Z;w==16?w=L?15:51:w==63?(V.pn=void 0,V.PV=function(){return V.pn?V.pn:V.pn=new V},w=38):w==51?w=(g+7^21)>=g&&(g+3^9)<g?63:38:w==89&&(E=nG(q,W,6)?!!W.capture:!!W,P=UO(51,P,18),w=94)}},Dq=function(g,V,q,J,P,t,W,y,L,n,E,A,Z,w){w=79;{Z=58;while(![]==0)try{if(w==85)break;else if(w==98)Z=58,T3(35,11,q,A,t),w=99;else if(w==79)w=94;else if(w==36)w=96;else if(w==30)t=vL(g,52,V,q),(P=t>=0)&&Array.prototype.splice.call(V,t,g),E=P,w=46;else if(w==81)E=n,w=7;else if(w==75)L=t.K,L(function(){T(21,V,P,t,P)}),w=81;else if(w==99)Z=58,w=31;else{if(w==59)return E;w==46?w=(J<<1&6)==2?13:59:w==31?w=W&&t.K?75:90:w==19?(Z=59,n=Pu(y,t,g,P),w=99):w==90?w=96:w==7?w=J+2>>4>=2&&(J>>2&4)<2?30:46:w==94?w=(J&117)==J?36:7:w==89?(t.K=null,y=t.P.pop(),w=19):w==96?w=t.P.length?89:81:w==13&&(q.HV(function(b){P=b},g,V),E=P,w=59)}}catch(b){if(Z==58)throw b;Z==59&&(A=b,w=98)}}},h=function(g,V,q,J,P,t,W,y,L,n,E,A,Z){{Z=41;while(Z!=72)if(Z==33){if((P=q,t=M.trustedTypes,t)&&t.createPolicy){try{P=t.createPolicy(g,{createHTML:La,createScript:La,createScriptURL:La})}catch(w){if(M.console)M.console[V](w.message)}A=P}else A=P;Z=30}else if(Z==69)h(0,V[L],false,8,P,t,W,y),Z=42;else if(Z==83)Z=L<V.length?69:80;else if(Z==21){a:{for(y=q;y<g.length;++y)if(W=g[y],!W.vV&&W.listener==V&&W.capture==!!t&&W.on==P){A=y;break a}A=-1}Z=92}else if(Z==53)Z=(J+1&58)>=J&&J-9<<2<J?27:80;else if(Z==92)Z=(J^37)>>3==1?95:26;else if(Z==52)Z=J-7>>4>=2&&(J>>1&16)<1?21:92;else if(Z==27)Z=t&&t.once?1:98;else if(Z==95)A=!!(P=g.Gm,(V|q)+(~P^V)-(~P|V)),Z=26;else if(Z==81)P=UO(51,P,6),W&&W[rN]?W.C.add(String(V),P,q,nG(null,t,7)?!!t.capture:!!t,y):mz(8,null,false,V,P,y,W,t,q),Z=80;else if(Z==26)Z=(J>>2&6)==4?33:30;else if(Z==41)Z=53;else if(Z==32){if(((W=(y=(n=(E=(V||t.S8++,t.In>0&&t.uS&&t.Y7&&t.e8<=1&&!t.I&&!t.K&&(!V||t.wJ-P>1)&&document.hidden==0),t.S8)==4)||E?t.G():t.AN,y-t.AN),t).H+=W>>q>0,t.T&&(t.T^=(t.H+1>>2)*(W<<2)),t).g=t.H+1>>2!=0||t.g,n||E)t.S8=0,t.AN=y;Z=(E?(t.In>t.O5&&(t.O5=t.In),y-t.wx<t.In-(g?255:V?5:2)?A=false:(t.wJ=P,L=Q(V?131:10,t),R(t,10,t.W),t.P.push([w8,L,V?P+1:P,t.F,t.B]),t.K=G3,A=true)):A=false,52)}else if(Z==54)L=g,Z=96;else if(Z==98)Z=Array.isArray(V)?54:81;else if(Z==80)Z=(J>>2&5)==4?32:52;else if(Z==96)Z=83;else{if(Z==30)return A;Z==42?(L++,Z=83):Z==1&&(Vs(9,g,null,y,V,W,t,P),Z=80)}}},OO=function(g,V,q,J,P,t,W,y){for(W=83;W!=35;)if(W==1)W=(V|7)>=3&&(V-7&7)<g?39:7;else if(W==20)y=function(){},y.prototype=P.prototype,J.v=P.prototype,J.prototype=new y,J.prototype.constructor=J,J.qg=function(L,n,E){for(var A=14;A!=51;){if(A==37)return P.prototype[n].apply(L,Z);if(A==16)w++,A=92;else if(A==14)var Z=Array(arguments.length-(A=89,q)),w=q;else A==19?(Z[w-q]=arguments[w],A=16):A==89?A=92:A==92&&(A=w<arguments.length?19:37)}},W=1;else if(W==83)W=78;else{if(W==7)return t;W==78?W=(V-4&4)>=3&&(V-4&8)<g?20:1:W==39&&(W=7)}},Vs=function(g,V,q,J,P,t,W,y,L,n,E,A,Z,w,b,I){{b=91;while(b!=40)if(b==49)J=new N$(q,this),W=V.on||V.src,t=V.listener,V.zm&&XD(5,null,0,V),P=t.call(W,J),b=46;else if(b==24)P=true,b=46;else if(b==54)L=V,b=82;else if(b==94)L++,b=59;else if(b==46)I=P,b=80;else if(b==25)b=g-8&13?55:64;else if(b==22){a:{for(A=(w=(n=fG,t).split(P),V);A<w.length-J;A++){if(L=w[A],!(L in n))break a;n=n[L]}(y=(E=w[w.length-J],n)[E],Z=W(y),Z!=y&&Z!=q)&&YB(n,E,{configurable:true,writable:true,value:Z})}b=33}else if(b==64)b=55;else if(b==55)b=(g&105)==g?15:29;else if(b==15)b=Array.isArray(P)?54:36;else if(b==36)y=UO(51,y,16),t&&t[rN]?t.C.add(String(P),y,true,nG(q,W,5)?!!W.capture:!!W,J):mz(12,null,false,P,y,J,t,W,true),b=29;else if(b==91)b=69;else if(b==82)b=59;else if(b==57)b=W?22:33;else if(b==14)Vs(33,0,null,J,P[L],t,W,y),b=94;else if(b==59)b=L<P.length?14:29;else if(b==69)b=(g&30)==g?62:25;else{if(b==33)return I;b==29?b=(g&102)==g?63:80:b==63?b=V.vV?24:49:b==80?b=((g^83)&7)==4?57:33:b==62&&(b=25)}}},T3=function(g,V,q,J,P,t,W){{W=29;while(W!=7)if(W==83)P.A=((P.A?P.A+"~":"E:")+J.message+":"+J.stack).slice(0,q),W=g;else if(W==29)W=26;else if(W==g)W=V-5>>3==1?44:67;else if(W==44)this.g=q,W=67;else if(W==26)W=(V|5)>>3==1?83:g;else if(W==67)W=(V+6^26)>=V&&(V-3|19)<V?45:36;else if(W==45)this.type=q,this.currentTarget=this.target=J,this.defaultPrevented=this.hN=false,W=36;else if(W==36)return t}},EG=function(g,V,q,J,P,t,W,y,L,n,E,A){for(E=46;E!=61;)if(E==34)E=V-8<30&&(V|9)>=14?g:12;else if(E==31)E=t===""||t==void 0?14:72;else if(E==72)P.setAttribute(y,t),E=34;else if(E==14)E=g8?6:54;else if(E==46)E=79;else if(E==54)L={},g8=(L.atomic=false,L.autocomplete="none",L.dropeffect="none",L.haspopup=false,L.live="off",L.multiline=false,L.multiselectable=false,L.orientation="vertical",L.readonly=false,L.relevant="additions text",L.required=false,L.sort="none",L.busy=false,L.disabled=false,L[q]=false,L.invalid="false",L),E=6;else if(E==39)this[this+""]=this,A=Promise.resolve(),E=60;else if(E==60)E=V>>1&6?34:63;else if(E==g){a:{switch(y){case t:A=W?"disable":"enable";break a;case 2:A=W?"highlight":"unhighlight";break a;case P:A=W?"activate":"deactivate";break a;case q:A=W?"select":"unselect";break a;case 16:A=W?"check":"uncheck";break a;case 32:A=W?"focus":"blur";break a;case J:A=W?"open":"close";break a}throw Error("Invalid component state");}E=12}else if(E==63)Array.isArray(t)&&(t=t.join(J)),y="aria-"+W,E=31;else if(E==6)n=g8,W in n?P.setAttribute(y,n[W]):P.removeAttribute(y),E=34;else if(E==79)E=V+1>>3==1?39:60;else if(E==12)return A},LG=function(g,V,q,J,P,t,W,y,L){{y=82;while(y!=35)if(y==82)y=16;else if(y==93)this.nn=this.nn,this.j=this.j,y=43;else if(y==43)y=V-1<<1>=V&&(V-8^19)<V?50:90;else{if(y==90)return L;if(y==6)this.Tm=M.document||document,y=34;else if(y==63)y=(V^37)>>g?43:93;else if(y==50){if(t=J.length,t>q){for(W=(P=Array(t),q);W<t;W++)P[W]=J[W];L=P}else L=[];y=90}else y==34?y=(V|8)>>3==3?81:63:y==81?(J=q[wN],L=J instanceof sO?J:null,y=63):y==16&&(y=(V&74)==V?6:34)}}},nG=function(g,V,q,J,P,t){{P=39;while(P!=5){if(P==80)return t;P==84?(t=Wu[g](Wu.prototype,{pop:V,parent:V,length:V,stack:V,floor:V,splice:V,console:V,call:V,propertyIsEnumerable:V,document:V,replace:V,prototype:V}),P=80):P==24?P=(q|8)>=11&&((q^12)&15)<2?14:34:P==35?P=(q-3|37)<q&&q-9<<1>=q?84:80:P==14?(g.Y1&&g.Y1.forEach(V,void 0),P=34):P==34?P=q<<1<15&&(q+7&7)>=0?45:35:P==45?(J=typeof V,t=J=="object"&&V!=g||J=="function",P=35):P==39&&(P=24)}}},ys=function(g,V,q,J,P,t,W,y,L){{y=54;while(y!=57)if(y==39)this.listener=t,this.proxy=null,this.src=q,this.type=J,this.capture=!!P,this.on=W,this.key=++AU,this.zm=this.vV=false,y=73;else if(y==g)y=(V-9^14)>=V&&(V+9&43)<V?39:73;else if(y==54)y=19;else if(y==24)L=q&&q.parentNode?q.parentNode.removeChild(q):null,y=61;else if(y==73)y=(V&93)==V?5:62;else if(y==19)y=(V&121)==V?50:g;else if(y==5)L=!!(t.WV&P)&&h(t,P,0,41)!=J&&(!(t.SP&P)||t.dispatchEvent(EG(91,6,8,64,4,q,J,P)))&&!t.j,y=62;else if(y==62)y=((V|6)&13)>=6&&(V>>2&8)<1?24:61;else if(y==63)L=Math.floor(this.G()),y=64;else if(y==61)y=(V<<2&13)>=12&&V>>1<20?63:64;else if(y==50)sG.call(this,q?q.type:""),this.relatedTarget=this.currentTarget=this.target=null,this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0,this.key="",this.charCode=this.keyCode=0,this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=false,this.state=null,this.pointerId=0,this.pointerType="",this.timeStamp=0,this.J=null,q&&this.init(q,J),y=g;else if(y==64)return L}},Zq=function(g,V,q,J,P,t,W,y,L,n,E,A,Z){{A=73;while(A!=15)if(A==27)this.N=W,A=86;else if(A==85)A=2;else if(A==34)A=(V-5^9)<V&&(V+1^19)>=V?74:64;else if(A==72)Z=function(w){return q.call(Z.src,Z.listener,w)},q=cL,E=Z,A=34;else if(A==2)A=n?9:58;else if(A==48)A=(V>>1&8)<5&&V-8>>3>=2?72:34;else if(A==62)n=(L=Object.getPrototypeOf(n.prototype))&&L.constructor,A=31;else if(A==74)this.src=q,this.Cn=0,this.h={},A=64;else if(A==g)A=58;else if(A==1)A=(V>>1&3)==1?36:93;else if(A==80)A=(t=dN[y])?g:62;else{if(A==86)return E;A==36?(E=q,A=93):A==64?A=V-6<<1<V&&(V-1^17)>=V?8:86:A==73?A=1:A==66?(E=Math.floor(this.yk+(this.G()-this.wx)),A=48):A==93?A=(V|48)==V?66:48:A==29?(n=this.constructor,A=85):A==31?A=2:A==58?(W=t?typeof t.PV==="function"?t.PV():new t:null,A=27):A==50?A=(W=J)?27:29:A==9?(y=jB(78,11,n),A=80):A==8&&(HL.call(this,P),A=50)}}},tR=function(g,V,q,J,P,t,W,y,L,n,E,A,Z){{A=88;while(A!=30)if(A==88)A=33;else if(A==61)this.n++,q=V-this.V,this.V+=q/this.n,this.F6+=q*(V-this.V),A=94;else if(A==95)E+=E<<3,E=(n=E>>11,~(E&n)-~E+(~E&n)),t=E+(E<<15)>>>0,y=new Number((W=(L=1<<q,-~(L&1)+~(L|1)+2*(L&-2)),-2*~(t&W)+~(t|W)+2*(t^W)+(~t^W))),y[0]=(t>>>q)%J,Z=y,A=64;else if(A==65)Z=q in qW?qW[q]:qW[q]=V+q,A=69;else if(A==55)E=P=0,A=27;else if(A==71)P++,A=2;else{if(A==64)return Z;A==27?A=2:A==2?A=P<V.length?73:95:A==56?A=g-1>>4>=0&&g+7<17?65:69:A==85?A=78:A==69?A=(g-1|49)<g&&(g-5|95)>=g?55:64:A==73?(E+=V.charCodeAt(P),E+=E<<10,E^=E>>6,A=71):A==33?A=(g>>2&15)==3?61:94:A==54?(this[this+""]=this,A=56):A==94?A=(g-2|84)<g&&(g+3^9)>=g?85:78:A==78&&(A=(g<<1&23)==4?54:56)}}},S=function(g,V,q,J,P,t){for(t=88;t!=39;)if(t==70)t=(V+6^21)<V&&(V+4^28)>=V?56:34;else{if(t==29)return P;t==4?(pG.call(this),g||uY||(uY=new hR),this.CT=this.ZC=null,this.lS=void 0,this.S=null,this.Kn=false,this.Y1=this.tN=null,this.iN=false,t=29):t==56?(J.P.splice(g,g,q),t=34):t==88?t=96:t==96?t=(V&45)==V?33:70:t==33?(R(g,q,J),J[QC]=2796,t=70):t==34&&(t=(V+2^18)<V&&(V-7|42)>=V?4:29)}},UO=function(g,V,q,J,P,t,W,y,L,n,E,A){for(A=81;A!=g;){if(A==59)return E;A==39?A=(q|8)==q?11:59:A==17?A=q<<2&7?39:64:A==81?A=17:A==11?(E=(n=eB[V.substring(0,3)+"_"])?n(V.substring(3),J,P,t,W,y,L):q$(6,J,V,25),A=59):A==64&&(typeof V==="function"?E=V:(V[BL]||(V[BL]=function(Z){return V.handleEvent(Z)}),E=V[BL]),A=39)}},mz=function(g,V,q,J,P,t,W,y,L,n,E,A,Z,w,b,I){{I=73;while(I!=8){if(I==37)throw Error("Invalid event type");if(I==0)L++,I=41;else if(I==92)I=41;else if(I==58)I=A.proxy?54:31;else if(I==77)I=((g^2)&10)<2&&(g>>1&15)>=8?79:21;else if(I==74)W.attachEvent(tR(5,"on",J.toString()),E),I=91;else if(I==79)EO.call(this),this.C=new sO(this),this.U7=this,this.Nf=null,I=21;else if(I==31)E=Zq(41,37),A.proxy=E,E.src=W,E.listener=A,I=4;else{if(I==5)throw Error("addEventListener and attachEvent are unavailable.");if(I==83)I=g+1&15?77:59;else if(I==41)I=L<t.length?95:77;else if(I==49)W.addListener(E),I=91;else if(I==59)b=function(v){v&&W.appendChild(typeof v==="string"?y.createTextNode(v):v)},L=1,I=92;else if(I==89)Z=nG(V,y,5)?!!y.capture:!!y,(n=LG(4,16,W))||(W[wN]=n=new sO(W)),A=n.add(J,P,L,Z,t),I=58;else if(I==60)b(n),I=0;else if(I==18)I=g-3>=20&&g-2<29?6:83;else if(I==73)I=18;else{if(I==54)return w;if(I==21)I=(g&28)==g?23:54;else if(I==64){a:{if(n&&typeof n.length=="number"){if(nG(V,n,3)){E=typeof n.item=="function"||typeof n.item==P;break a}if(typeof n==="function"){E=typeof n.item=="function";break a}}E=J}I=(bY(45,q,"",b,21,E?LG(4,3,q,n):n),0)}else if(I==52)I=!bY(45,"object","array","number",3,n)||nG(V,n,3)&&n.nodeType>q?60:64;else if(I==90)I=W.attachEvent?74:16;else if(I==91)z3++,I=54;else if(I==16)I=W.addListener&&W.removeListener?49:5;else if(I==95)n=t[L],I=52;else if(I==23)I=J?89:37;else if(I==71)$B||(y=Z),y===void 0&&(y=q),W.addEventListener(J.toString(),E,y),I=91;else if(I==6){for(t in y=V,J.h){for(P=(W=J.h[t],V);P<W.length;P++)++y,k(7,q,W[P]);delete J.h[t],J.Cn--}I=83}else I==4&&(I=W.addEventListener?71:90)}}}}},q$=function(g,V,q,J,P,t,W,y,L,n,E){{n=98;while(n!=56)if(n==52)W=t.classList.contains(P),n=67;else if(n==98)n=27;else if(n==42)n=(J&30)==J?18:60;else if(n==81)V(function(A){A(q)}),E=[function(){return q},function(){}],n=42;else if(n==14)n=t.classList?52:25;else if(n==25)y=k(45,"string",t),W=vL(V,20,y,P)>=q,n=67;else if(n==60)n=(J-1&8)<3&&J<<2>=-33?14:2;else{if(n==2)return E;if(n==18){a:{for(L=(W=[q==typeof globalThis&&globalThis,t,q==typeof window&&window,q==typeof self&&self,q==typeof global&&global],V);L<W.length;++L)if((y=W[L])&&y[P]==Math){E=y;break a}throw Error("Cannot find global object");}n=60}else n==67?(E=W,n=2):n==27&&(n=(J-9|19)<J&&(J-g^9)>=J?81:42)}}},gN=function(g,V,q,J,P,t,W,y,L,n,E){{n=33;while(n!=60)if(n==59)E=typeof P.className==q?P.className:P.getAttribute&&P.getAttribute(J)||"",n=54;else if(n==45)n=(V<<2&15)>=8&&(V<<1&16)<6?59:54;else if(n==33)n=45;else if(n==36)n=(V&103)==V?7:g;else if(n==69)this.n===0?E=[0,0]:(this.L.sort(function(A,Z){return A-Z}),E=[this.n,this.L[this.L.length>>1]]),n=12;else if(n==9)W=J,W^=W<<13,W=(t=W>>17,-(t|0)+(W|t)+(~W&t)),W^=W<<5,(W=(W|0)-~(W&P)+~W)||(W=1),E=q^W,n=36;else if(n==54)n=(V-4^32)>=V&&V-8<<1<V?9:36;else{if(n==12)return E;n==7?(L=function(){},P=void 0,y=FD(q,function(A,Z){for(Z=67;Z!=47;)Z==77?(J&&G3(J),P=A,L(),L=void 0,Z=47):Z==67&&(Z=L?77:47)},!!J),t=y[1],W=y[0],E={top:function(A,Z,w,b,I,v,x){for(v=91;v!=39;)if(v==77)v=Z?32:69;else if(v==91)x=function(){P(function(m){G3(function(){A(m)})},w)},v=77;else{if(v==69)return I=W(w),A&&A(I),I;v==32?v=P?6:42:v==42?(b=L,L=function(){(b(),G3)(x)},v=39):v==6&&(x(),v=39)}},pe:function(A){t&&t(A)}},n=g):n==g&&(n=(V-8^22)>=V&&(V-9|66)<V?69:12)}}},e=function(g,V,q,J,P,t,W,y,L,n,E,A){if(((q>>2&7)==1&&(L=J&7,t=[14,-62,60,-48,-62,-86,t,-66,-9,75],y=bK,n=Wu[P.U](P.I8),n[P.U]=function(Z){L=(W=(L+=V+7*J,Z),(L|0)+~L-~(L|7))-(L^7)},n.concat=function(Z,w,b,I){return((W=(w=(b=g%16+1,3871*W+t[L+19&7]*g*b+L+49*W*W+(y()|0)*b-b*W)-49*g*g*W+1*g*g*b- -3038*g*W,Z=t[w],void 0),t)[(I=L+61,-(I|7)-2*~(I|7)+(I^7)+2*(~I^7))+(J&2)]=Z,t)[L+(-~(J&2)+(J&-3)+(~J|2))]=-62,Z},E=n),q<<1)>=4&&q+2<20){y=r(19,J);{W=0;while(g>0)W=(t=W<<8,P=KG(8,V,J),-~(t&P)+2*(t^P)+(~t^P)),g--}R(J,y,W)}return q<<1&(((q&29)==q&&(E=(P=g[J]<<24|g[(J|V)-~J+(~J|V)]<<16|g[-2-2*~(J|2)-(J^2)]<<8,t=g[-2-2*~(J|3)-(J^3)],2*(t|0)+~t-(~P|t))),(q|48)==q)&&(J=Wu[V.U](V.k7),J[V.U]=function(){return g},J.concat=function(Z){if(-0===Number())g=Z},E=J),15)||(E=A=function(){for(var Z=46;Z!=92;)if(Z==96)Z=t.l?54:58;else if(Z==95)Z=P==V?65:64;else if(Z==80){var w=T((S(0,10,b,t),33),254,false,t,false);Z=47}else if(Z==58)y&&L&&y.removeEventListener(L,A,In),Z=92;else{if(Z==47)return w;if(Z==54)var b=[zl,W,J,void 0,y,L,(Z=22,arguments)];else if(Z==65){var I=!t.P.length;Z=((S(0,43,b,t),I)&&T(36,254,false,t,false),47)}else Z==46?Z=t.g==t?96:92:Z==22?Z=P==g?80:95:Z==64&&(w=Pu(b,t,3,true),Z=47)}}),E},r=function(g,V,q,J,P,t){return(((g|1)>>4||(V.I?t=xB(V.X,V):(J=iY(true,V,8),(J|128)-(J&-129)-(~J&128)&&(J=(J&128)+2*~(J&128)-~J-(J|-129),P=iY(true,V,2),J=(q=J<<2,~q-3*~P+2*(q&~P)+2*(q|~P))),t=J)),g>>1)&1)==1&&(V.I?t=xB(V.X,V):(J=iY(true,V,8),(J|128)-(J&-129)-(~J&128)&&(J=(J&128)+2*~(J&128)-~J-(J|-129),q=iY(true,V,2),J=(P=J<<2,~P-3*~q+2*(P&~q)+2*(P|~q))),t=J)),t},bY=function(g,V,q,J,P,t,W,y,L,n,E){for(E=41;E!=64;)if(E==72)y in L&&J.call(void 0,L[y],y,t),E=24;else if(E==77)E=((P^64)&14)==2?46:99;else if(E==46)W=typeof t,y=W!=V?W:t?Array.isArray(t)?"array":W:"null",n=y==q||y==V&&typeof t.length==J,E=99;else if(E==27)E=y<W?72:77;else if(E==42)E=69;else if(E==92)E=19;else if(E==4)E=(P-3&14)==2?90:77;else if(E==24)y++,E=27;else{if(E==g)return n;E==55?(y-=8,t.push(W>>y&255),E=92):E==40?(J=0,t=[],y=0,E=42):E==50?E=(P-1&5)>=4&&(P|6)<23?28:4:E==71?(n=t,E=g):E==93?(y+=V,W=W<<V|q[J],E=6):E==6?E=19:E==69?E=J<q.length?93:71:E==98?E=27:E==28?(y.classList?y.classList.remove(W):q$(6,t,V,5,W,y)&&k(36,"class",y,Array.prototype.filter.call(k(49,J,y),function(A){return A!=W}).join(q)),E=4):E==58?(J++,E=69):E==41?E=50:E==90?(W=t.length,L=typeof t==="string"?t.split(q):t,y=V,E=98):E==99?E=((P^78)&13)==1?40:g:E==19&&(E=y>7?55:58)}},Pu=function(g,V,q,J,P,t,W,y,L,n,E,A){E=g[0];switch(!(E==oR)){case true:if(E==SB){n=g[1];try{t=V.A||V.R(g)}catch(Z){T3(35,9,2048,Z,V),t=V.A}(n((W=V.G(),t)),V).u+=V.G()-W}else if(E==w8)g[q]&&(V.F=J),g[4]&&(V.B=J),V.R(g);else switch(!(E==$n)){case 0==![""]:if(E==mX){try{for(L=0;L<V.s5.length;L++)try{A=V.s5[L],A[0][A[1]](A[2])}catch(Z){}}catch(Z){}((0,g[1])(function(Z,w){V.HV(Z,J,w)},function(Z){(S(0,11,[(Z=!V.P.length,vu)],V),Z)&&T(24,254,J,V,false)},function(Z){return V.Mf(Z)},(V.s5=[],P=V.G(),function(Z,w,b){return V.TW(Z,w,b)})),V).u+=V.G()-P}else{if(E==zl)return y=g[2],B(32,g[6],V),B(449,y,V),V.R(g);E==vu?(V.R(g),V.l=null,V.bS=[],V.gx=[]):E==QC&&M.document.readyState==="loading"&&(V.K=function(Z,w){function b(I){{I=75;while(I!=89)I==75?I=w?89:37:I==37&&(w=J,M.document.removeEventListener("DOMContentLoaded",b,In),M.removeEventListener("load",b,In),Z(),I=89)}}(M.document.addEventListener("DOMContentLoaded",b,(w=false,In)),M).addEventListener("load",b,In)})}break;case false:V.F=J,V.R(g);break}break;case ![]==true:V.B=J,V.lN=25,V.R(g);break}},Q=function(g,V,q){if((q=V.l[g],q)===void 0)throw[xn,30,g];if(q.value)return q.create();return q.create(g*1*g+-62*g+-79),q.prototype},d8=function(){return tR.call(this,94)},HL=function(g){return S.call(this,g,16)},D4=function(g,V,q,J){Gl(V,(J=r(12,(q=r(31,V),V)),J),N(g,F(q,V)))},an=function(g,V,q,J,P,t,W,y){(V.push(g[0]<<24|g[1]<<16|g[2]<<8|g[3]),V).push((q=g[4]<<24,P=g[5]<<16,-~P+2*(q^P)+(~q^P)-(~q&P))|g[6]<<8|g[7]),V.push((y=(J=g[8]<<24,t=g[9]<<16,2*(J&t)+~t-2*(~J^t)+(~J|t))|g[10]<<8,W=g[11],-~W+(y^W)+(y|~W)))},kn=function(g,V){{var q=63;while(q!=15)if(q==97){var J=arguments[t];for(W in J)g[W]=J[W];var P=(q=54,0)}else if(q==42)t++,q=64;else if(q==32)P++,q=17;else if(q==65)q=64;else if(q==63)var t=(q=65,1);else if(q==54)q=17;else if(q==7){var W=iK[P];Object.prototype.hasOwnProperty.call(J,W)&&(g[W]=J[W]),q=32}else q==64?q=t<arguments.length?97:15:q==17&&(q=P<iK.length?7:42)}},La=function(g){return Zq.call(this,41,18,g)},lK=function(g,V,q,J,P,t,W,y,L,n){if(y=P[on]||{},"B")n=r(34,P);for(L=K(V,(y.Z7=r(g,P),y.D=[],t=P.g==P?(KG(8,true,P)|J)-q:1,P)),W=J;W<t;W++)y.D.push(r(13,P));while(t--)y.D[t]=O(y.D[t],P);return y.ht=F(n,P),y.zW=F(L,P),y},XN=function(g,V,q,J,P){if(g.length==3){{J=0;while(J<3)V[J]+=g[J],J++}P=0;{q=[13,8,13,12,16,5,3,10,15];while(P<9)V[3](V,P%3,q[P]),P++}}},r8=function(g,V,q,J,P,t,W,y){y=P[3]|(t=0,0);{W=P[2]|0;while(t<15)y=y>>>8|y<<g,q=q>>>8|q<<g,q+=J|0,J=J<<3|J>>>V,q^=W+3277,J^=q,y+=W|0,y^=t+3277,W=W<<3|W>>>V,W^=y,t++}return[J>>>g&255,J>>>16&255,J>>>8&255,J>>>0&255,q>>>g&255,q>>>16&255,q>>>8&255,q>>>0&255]},xB=function(g,V,q){return(q=g.create().shift(),V).I.create().length||V.X.create().length||(V.X=void 0,V.I=void 0),q},Qs=function(g,V,q,J,P){return ys.call(this,71,42,V,q,J,g,P)},kB=function(g,V,q,J,P){J=typeof q;switch(!(J==V)){case true:if(J=="function"&&typeof q.call=="undefined")return V;break;case false:if(q){if(q instanceof Array)return"array";if(q instanceof Object)return J;if(P=Object.prototype.toString.call(q),P=="[object Window]")return V;if(P=="[object Array]"||typeof q.length=="number"&&typeof q.splice!="undefined"&&typeof q.propertyIsEnumerable!="undefined"&&!q.propertyIsEnumerable("splice"))return"array";if(P=="[object Function]"||typeof q.call!="undefined"&&typeof q.propertyIsEnumerable!="undefined"&&!q.propertyIsEnumerable("call"))return"function"}else return g;break}return J},YB=typeof Object.defineProperties=="function"?Object.defineProperty:function(g,V,q,J){{J=95;while(J!=97)if(J==95)J=g==Array.prototype||g==Object.prototype?37:88;else{if(J==37)return g;if(J==88)return g[V]=q.value,g}}},eO=function(g,V,q,J,P,t,W,y,L,n){if(J.g==J){{n=Q(q,J),q==1||q==275||q==330?(W=function(E,A,Z,w,b,I,v,x){for(v=(I=67,37);;)try{if(I==71)break;else if(I==70)I=n.cV!=b?81:11;else if(I==67)Z=n.length,b=-2*~(Z&4)+~Z+-5+2*(Z&-5)>>3,I=70;else if(I==11)n.push(n.fn[-7+(Z|7)-~(Z&7)+(~Z|7)]^E),I=71;else if(I==16)v=40,n.fn=r8(24,29,e(n,1,21,(w|0)+4),e(n,1,25,w),A),I=11;else if(I==81)A=[0,0,L[1],L[2]],n.cV=b,w=(b<<3)-4,I=16;else if(I==91)throw v=37,x;}catch(m){if(v==37)throw m;v==40&&(x=m,I=91)}},L=O(g,J)):W=function(E){n.push(E)},P&&W(-(P|255)-2*~P+(P^255)+2*(~P|255)),y=0,t=V.length;while(y<t)W(V[y]),y++}}},OG=function(g,V,q,J,P){return JR.call(this,"string",12,V,g,q,J,P)},hU=function(g,V,q,J,P,t,W,y,L,n){if(q.yf=SO,true)q.s7=Tl;L=(q.k7=(q.I8=nG(q.U,{get:function(){return this.concat()}},(q.fT=q[SB],40)),Wu[q.U](q.I8,{value:{value:{}}})),0);{y=[];while(L<259)y[L]=String.fromCharCode(L),L++}if(q.j8=false,((q.wJ=8001,q.X=void 0,q.W=0,q).A=void 0,q).e8=0,q.a8=W,true)q.F=false;(n=(q.lN=25,(q.PE=(q.g=(q.In=0,q),[]),window).performance||{}),q).uS=(q.B=false,q.u=0,false);while("g")if(q.Y7=false,true)break;q.K=(q.AN=0,null);while(!null)if(q.yk=0,"Q")break;if(((q.O5=(q.H=1,0),q).bS=(q.P=(q.Qk=(q.o8=[],[]),q.wx=0,[]),q.WE=(q.U5=false,q.I=(q.s5=[],void 0),q.BV=void 0,q.mt=(q.l=[],0),q.jP=P,q.gx=[],q.gJ=void 0,q.S8=void 0,q.O=void 0,q.x7=function(E){return T3.call(this,35,16,E)},(q.T=void 0,n).timeOrigin||(n.timing||{}).navigationStart||0),[]),V&&V.length==2)&&(q.PE=V[0],q.o8=V[1]),g)try{q.BV=JSON.parse(g)}catch(E){q.BV={}}R(q,295,(R(q,(S(q,33,313,(S(q,36,(S(q,5,274,(d(q,(B(10,0,(S(q,41,243,(S(q,32,(S(q,8,486,function(E,A,Z,w,b,I,v,x,m){for(m=67;m!=42;)m==67?(v=K(26,E),b=K(23,127,E),A=[],w=O(477,E),I=0,Z=w.length,m=47):m==83?m=18:m==82?(B(v,A,E),m=42):m==18?m=b--?41:82:m==47?m=18:m==41&&(I=(x=K(22,127,E),2*(I&x)+(I&~x)+(~I&x))%Z,A.push(w[I]),m=83)}),432),function(E,A,Z,w,b,I,v,x,m){for(m=60;m!=37;)m==46?m=x<A.length?99:82:m==99?(w+=String.fromCharCode(A[x]^121),m=86):m==82?(R(E,I,Z[w]),m=37):m==86?(x++,m=46):m==22?m=46:m==60&&(v=r(8,E),b=r(8,E),I=r(8,E),Z=O(v,E),A=O(b,E),w="",x=0,m=22)}),function(E,A,Z,w,b,I,v,x,m){{m=56;while(m!=46)m==35?(v+=String.fromCharCode(Z[A]^121),m=37):m==18?m=52:m==52?m=A<Z.length?35:69:m==37?(A++,m=52):m==56?(I=K(33,E),x=K(51,E),b=r(27,E),Z=O(I,E),w=Q(x,E),v="",A=0,m=18):m==69&&(R(E,b,v in w|0),m=46)}})),q)),131),0),function(E,A,Z,w,b,I,v,x,m){for(m=57;m!=89;)m==57?(b=K(26,E),I=K(7,127,E),v="",x=O(477,E),A=x.length,w=0,m=26):m==26?m=34:m==34?m=I--?55:85:m==17?m=34:m==55?(w=(Z=K(6,127,E),(w|Z)-~Z+(w|~Z))%A,v+=y[x[w]],m=17):m==85&&(d(E,b,v),m=89)})),S(q,8,219,function(E){D4(4,E)}),346),function(E,A,Z,w,b,I,v,x,m,u){{u=77;while(u!=50)u==27?(m=lK(19,49,1,0,E.g),I=m.zW,w=m.D,Z=m.ht,x=w.length,b=m.Z7,v=x==0?new I[Z]:x==1?new I[Z](w[0]):x==2?new I[Z](w[0],w[1]):x==3?new I[Z](w[0],w[1],w[2]):x==4?new I[Z](w[0],w[1],w[2],w[3]):2(),R(E,b,v),u=50):u==77&&(u=h(false,true,14,25,A,E)?50:27)}}),function(E,A,Z,w,b,I,v,x,m,u,X,U,G,c,D){{D=17;while(D!=97)if(D==48)D=kB("null","object",I)=="object"?15:27;else if(D==17)D=h(true,true,14,27,A,E)?97:94;else if(D==27)D=E.g==E?85:97;else if(D==66)D=u<b?56:97;else if(D==44)D=66;else if(D==85)U=U>0?U:1,u=0,b=I.length,D=44;else if(D==56)m(I.slice(u,(u|0)+(U|0)),v),D=36;else if(D==94)w=r(38,E),G=r(18,E),c=K(17,E),X=K(16,E),I=O(w,E),U=O(c,E),v=Q(X,E),m=Q(G,E),D=48;else if(D==15){for(x in Z=[],I)Z.push(x);I=Z,D=27}else D==36&&(u+=U,D=66)}})),444),0),[]));while(2)if(S(q,5,72,function(E,A,Z,w,b){for(b=87;b!=35;)b==21?(d(E,10,E.W),b=35):b==87?(A=E.Qk.pop(),b=67):b==67?b=A?7:21:b==11?b=w>0?99:68:b==99?(Z=r(5,E),A[Z]=E.l[Z],b=48):b==7?(w=KG(8,true,E),b=88):b==48?(w--,b=11):b==68?(A[482]=E.l[482],A[123]=E.l[123],E.l=A,b=35):b==88&&(b=11)}),{})break;R(q,400,(S(q,(S(q,41,245,(B(380,0,(S(((R((S(q,40,(R(q,(d(q,249,[(S(q,36,(S(q,33,(d(q,(S(q,36,47,(B(45,(S(q,(S(q,(R(q,(S(q,37,(S(q,8,29,(S(q,(S((S((S(q,40,62,(S(q,(S(q,33,(d(q,(d(q,(R(q,(S(q,8,372,function(E){D4(1,E)}),1),Rn(4)),322),[]),q.BE=0,124),134),186),function(E,A,Z,w,b,I){R((w=(I=F((Z=r(5,(A=(b=r(9,E),K(58,E)),E)),A),E),F(b,E)),E),Z,w in I|0)}),5),61,function(E,A,Z,w){d(E,(Z=K(27,(A=KG(8,!(w=K(43,E),0),E),E)),Z),O(w,E)>>>A)}),function(E,A){MW((A=O(r(34,E),E),123),10,0,104,E.g,A)})),q),9,211,function(E,A,Z,w){R((A=r(35,(Z=r((w=r(26,E),30),E),E)),E),A,Q(w,E)||Q(Z,E))}),q),37,320,function(E,A,Z,w,b){for(b=33;b!=73;)b==33?b=h(false,true,14,56,A,E)?73:21:b==21&&(w=K(42,E),Z=r(9,E),d(E,Z,function(I){return eval(I)}(Ka(Q(w,E.g)))),b=73)}),36),278,function(E,A,Z,w,b,I,v,x,m,u,X,U,G,c,D,lY,IR,l,yC){for(l=40;l!=71;)l==31?(I=u,m=[],l=0):l==18?(c=0,l=9):l==7?(c++,l=66):l==96?l=U<Z?56:18:l==28?(D++,l=27):l==40?(yC=function(H,WL){for(;G<H;)IR|=KG(8,true,E)<<G,G+=8;return IR>>=(WL=IR&(1<<(G-=H,H))-1,H),WL},lY=r(5,E),G=IR=0,u=(v=yC(3),-4-2*~v-(v^1)-2*(v|-2)),Z=yC(5),b=[],D=w=0,l=13):l==9?l=66:l==69?(m.push(Q(r(12,E),E)),l=86):l==32?l=96:l==56?(b[U]||(X[U]=yC(x)),l=82):l==13?l=27:l==84?(S(E,9,lY,function(H,WL,C,VC,PL,p){for(p=8;p!=83;)p==52?p=43:p==3?(WL.push(C),p=61):p==36?p=77:p==43?p=PL<Z?93:81:p==93?(C=X[PL],p=40):p==81?(H.I=e(m.slice(),H,51),H.X=e(WL,H,50),p=83):p==1?p=77:p==8?(WL=[],PL=0,VC=[],p=52):p==34?(C=VC[C],p=3):p==88?(VC.push(K(27,H)),p=36):p==40?p=b[PL]?3:1:p==77?p=C>=VC.length?88:34:p==61&&(PL++,p=43)}),l=71):l==23?l=I--?69:84:l==27?l=D<Z?41:89:l==82?(U++,l=96):l==90?(b[c]&&(X[c]=K(32,E)),l=7):l==66?l=c<Z?90:31:l==89?(x=((w|0)-1).toString(2).length,X=[],U=0,l=32):l==41?(A=yC(1),b.push(A),w+=A?0:1,l=28):l==0?l=23:l==86&&(l=23)}),function(E,A,Z,w,b){B((b=kB("null",(w=F((A=r(13,(Z=r(23,E),E)),Z),E),"object"),w),A),b,E)})),355),function(E,A,Z,w,b,I){{I=77;while(I!=39)I==40?(b=lK(19,49,1,0,E),w=b.zW,Z=b.ht,I=88):I==77?I=h(false,true,14,24,A,E)?39:40:I==75?(d(E,b.Z7,Z.apply(w,b.D)),E.AN=E.G(),I=39):I==88&&(I=E.g==E||Z==E.x7&&w==E?75:39)}}),123),[2048]),41),297,function(E,A,Z,w,b,I,v,x){{x=11;while(x!=69)x==27?x=A==2?43:69:x==11?(w=K(32,E),I=K(33,E),b=r(18,E),x=3):x==43?(E.T=iY(false,E,32),E.O=void 0,x=69):x==3?x=E.g==E?16:69:x==24?(E.O=void 0,x=27):x==39?x=w==302?24:69:x==16&&(Z=Q(w,E),v=O(b,E),A=F(I,E),Z[A]=v,x=39)}}),9),203,function(E,A,Z,w){w=(Z=K(42,(A=r(13,E),E)),F(Z,E)),Q(A,E)!=0&&d(E,10,w)}),[]),q),function(E,A,Z,w,b,I,v,x){while(true)if(w=r(12,E),!!null==0)break;(I=O((v=(Z=F((b=r((A=K(35,E),x=r(27,E),5),E),A),E),F(x,E)),b),E),R)(E,w,e(2,1,40,v,I,E,Z))})),482),[]),98),function(E,A,Z,w,b,I){R(E,(A=(w=(I=r(13,(b=r(31,E),Z=r(22,E),E)),Q(Z,E)),O)(b,E)==w,I),+A)}),443),function(E){e(4,true,10,E)}),0),0,0]),27),q),327),function(E,A,Z,w,b,I){(I=(A=r((b=r(39,(Z=r(12,E),E)),26),E),Q(Z,E)),w=F(b,E),d)(E,A,I[w])}),q),39,[154,0,0]),q).HE=0,q),40,73,function(){}),q)),function(E,A,Z,w,b,I,v,x,m,u,X){for(X=71;X!=21;)X==71?(Z=r(9,E),A=K(50,E),m=K(48,E),v=K(34,E),I=O(v,E),x=O(A,E),w=O(Z,E.g),b=Q(m,E),X=23):X==23?X=w!==0?59:21:X==59&&(u=e(2,1,32,I,1,E,b,w,x),w.addEventListener(x,u,In),F(45,E).push(function(){w.removeEventListener(x,u,In)}),R(E,380,[w,x,u]),X=21)})),32),187,function(E){pa(4,E)}),[]));while(!""==!(![]!=0))if(S(q,33,181,function(E){pa(3,E)}),true)break;S(q,37,68,function(E,A,Z){R(E,(A=r(35,(Z=K(17,E),E)),A),""+Q(Z,E))});while({})if(S(q,5,390,function(E,A){A=K(34,E),B(A,[],E)}),14)break;T((S(0,42,(S(0,10,(S(0,(S(q,32,248,(q.FL=(B(449,(d(q,330,Rn((B(312,(R(((new (S(q,9,283,(S(q,32,460,function(E,A,Z,w,b){R(E,(Z=(b=K(59,(A=K(16,E),E)),w=Q(A,E),Q(b,E)),b),Z+w)}),function(E,A,Z){(A=(Z=r(9,E),Q)(Z,E.g),A[0]).removeEventListener(A[1],A[2],In)})),uK)("Submit")).dispose(),q),275,Rn(4)),M),q),4))),{}),q),0),function(E,A,Z,w,b,I){{I=10;while(I!=68)I==33?I=A<w?85:81:I==31?I=33:I==1?(A++,I=33):I==85?(b.push(KG(8,true,E)),I=1):I==10?(Z=K(35,E),w=K(38,127,E),b=[],A=0,I=31):I==81&&(B(Z,b,E),I=68)}})),11),[QC],q),[$n,J]),q),[mX,t]),q),20),254,true,q,true)},FN=function(g,V){function q(){this.F6=this.V=this.n=0}q.prototype.qf=function(J,P){return tR.call(this,12,J,P)};while(true)if(q.prototype.At=function(){return XD.call(this,12)},true)break;return[function(J){g.qf(J),V.qf(J)},(V=(g=new q,new q),function(J){J=[g.At(),V.At(),g.V,V.V];while(true)if(V=new q,true)break;return J})]},sO=function(g){return Zq.call(this,41,13,g)},pG=function(){return mz.call(this,18)},B=function(g,V,q){switch(!(g==10||g==131)){case true:if(q.U5&&g!=302)return;g==39||g==1||g==400||g==330||g==482||g==295||g==322||g==249||g==275||g==123?q.l[g]||(q.l[g]=e(g,6,37,62,q,V)):q.l[g]=e(g,6,39,17,q,V);break;case false:q.l[g]?q.l[g].concat(V):q.l[g]=e(V,q,49);break}g==302&&(q.T=iY(false,q,32),q.O=void 0)},cu=function(g,V,q,J,P,t,W,y,L,n,E,A){for(n=y=(J=(L=g.replace(/\\r\\n/g,"\\n"),[]),0);y<L.length;y++)P=L.charCodeAt(y),P<128?J[n++]=P:(P<2048?J[n++]=(E=P>>6,-~E+(E^192)+(~E|192)):(-~P-2*(P&-64513)+(P^64512)+(P|-64513)==55296&&y+1<L.length&&(L.charCodeAt(y+1)&64512)==56320?(P=(q=(P&1023)<<10,2*(65536|q)- -1-(-65537&q)+(-65537|q))+(L.charCodeAt(++y)&1023),J[n++]=P>>18|240,J[n++]=P>>12&63|128):J[n++]=(A=P>>12,V+(A^V)-(~A&V)),J[n++]=(t=P>>6,(t|0)+(~t^63)-(t|-64))|128),J[n++]=(W=P&63,(W&128)-1-(~W^128)));return J},d=function(g,V,q){if(V==10||V==131)g.l[V]?g.l[V].concat(q):g.l[V]=e(q,g,52);else{if(g.U5&&V!=302)return;V==39||V==1||V==400||V==330||V==482||V==295||V==322||V==249||V==275||V==123?g.l[V]||(g.l[V]=e(V,6,36,62,g,q)):g.l[V]=e(V,6,68,17,g,q)}V==302&&(g.T=iY(false,g,32),g.O=void 0)},jO=function(g,V,q,J,P,t,W,y,L,n,E,A,Z){if(!J.U5&&(A=void 0,q&&q[0]===xn&&(g=q[1],A=q[2],q=void 0),E=O(482,J),E.length==0&&(Z=O(131,J)>>3,E.push(g,Z>>8&255,Z&255),A!=void 0&&E.push(A&255)),L="",q&&(q.message&&(L+=q.message),q.stack&&(L+=":"+q.stack)),y=Q(V,J),y[0]>3)){J.g=(t=(L=cu((L=L.slice(0,(P=y[0],(P^3)+2*(~P^3)-2*(~P|3))),y[0]-=(L.length|0)+3,L),224),J).g,J);try{J.j8?(n=(n=O(295,J))&&n[n.length-1]||95,(W=F(322,J))&&W[W.length-1]==n||eO(249,[(n|255)- -2+(n^255)+2*(~n^255)],322,J)):Gl(J,295,[95]),Gl(J,1,N(2,L.length).concat(L),51)}finally{J.g=t}}},M$=function(){return OO.call(this,2,7)},Hu=function(){return Vs.call(this,42)},Ca=function(g){return ys.call(this,71,14,g)},iY=function(g,V,q,J,P,t,W,y,L,n,E,A,Z,w,b,I,v){if(L=Q(10,V),L>=V.W)throw[xn,31];for(Z=(v=(b=(A=q,0),L),V.fT).length;A>0;)E=v>>3,P=v%8,w=8-(P|0),t=w<A?w:A,I=V.gx[E],g&&(n=V,W=v,n.O!=W>>6&&(n.O=W>>6,y=O(302,n),n.gJ=r8(24,29,n.O,n.T,[0,0,y[1],y[2]])),I^=V.gJ[E&Z]),b|=(I>>8-(P|0)-(t|0)&(1<<t)-1)<<(A|0)-(t|0),v+=t,A-=t;return B(10,(J=b,(L|0)+(q|0)),V),J},sG=function(g,V){return T3.call(this,35,26,g,V)},FD=function(g,V,q,J,P,t,W,y){return UO.call(this,51,g,9,V,q,J,P,t,W,y)},Gl=function(g,V,q,J,P,t,W,y,L){if(g.g==g)for(P=O(V,g),V==1||V==275||V==330?(L=function(n,E,A,Z,w,b,I,v){for(v=(b=4,57);;)try{if(b==52)break;else if(b==21)v=58,P.fn=r8(24,29,e(P,1,20,(A|0)+4),e(P,1,28,A),w),b=61;else if(b==60)A=(E<<3)-4,w=[0,0,t[1],t[2]],P.cV=E,b=21;else if(b==35)b=P.cV!=E?60:61;else{if(b==68)throw v=57,I;b==4?(Z=P.length,E=-2*~(Z&4)+~Z+-5+2*(Z&-5)>>3,b=35):b==61&&(P.push(P.fn[-7+(Z|7)-~(Z&7)+(~Z|7)]^n),b=52)}}catch(x){if(v==57)throw x;v==58&&(I=x,b=68)}},t=F(249,g)):L=function(n){P.push(n)},J&&L(-(J|255)-2*~J+(J^255)+2*(~J|255)),W=q.length,y=0;y<W;y++)L(q[y])},NW=function(g,V,q,J,P,t){return gN.call(this,56,32,g,V,q,J,P,t)},O=function(g,V,q){if((q=V.l[g],q)===void 0)throw[xn,30,g];switch(!q.value){case 0==![""]:0;break;case false:return q.create();break}return q.create(g*1*g+-62*g+-79),q.prototype},F=function(g,V,q){q=V.l[g];for(true;q===void 0;undefined){throw[xn,30,g];if([])break}for(undefined;q.value;(0).k){return q.create();if("z")break}if(q.create(g*1*g+-62*g+-79),{})return q.prototype},f,RR=function(g,V,q,J,P,t,W,y){return Zq.call(this,41,5,g,V,q,J,P,t,W,y)},qr=function(g,V,q,J,P,t){try{P=g[((V|0)+2)%3],g[V]=(J=g[V],t=g[(~(V&1)-3*~(V|1)-2*(V&-2)+2*(V|-2))%3],-(J&t)-~J+(J|~t))-(P|0)^(V==1?P<<q:P>>>q)}catch(W){throw W;}},Yn=function(g,V,q,J,P,t){while(NaN===NaN==![])if(t=O(q,V),{})break;V.gx&&t<V.W?(d(V,q,V.W),MW(123,10,0,104,V,J)):B(q,J,V);while(true)if(fa(g,V,123,P),true)break;return F(449,(d(V,q,t),V))},R=function(g,V,q){switch(!(V==10||V==131)){case true:switch(!(g.U5&&V!=302)){case true:![]!=(![]==Number());break;case ![]:return;break}V==39||V==1||V==400||V==330||V==482||V==295||V==322||V==249||V==275||V==123?g.l[V]||(g.l[V]=e(V,6,38,62,g,q)):g.l[V]=e(V,6,69,17,g,q);break;case ![""]!=0:g.l[V]?g.l[V].concat(q):g.l[V]=e(q,g,53);break}V==302&&(g.T=iY(false,g,32),g.O=void 0)},KG=function(g,V,q){return q.I?xB(q.X,q):iY(V,q,g)},EO=function(){return LG.call(this,4,40)},pa=function(g,V,q,J,P,t,W){Gl(V,(J=O((t=r(5,(W=(P=(g|(q=g&4,3))- -1+(~g^3),r(30,V)),V)),W),V),q&&(J=cu(""+J,224)),P&&eO(249,N(2,J.length),t,V),t),J)},gC=function(g,V){return CG.call(this,48,"_",g,V,16)},uK=function(g,V,q){return vL.call(this,g,9,V,q)},N=function(g,V,q,J){for(J=(g|0)-(q=[],1);J>=0;J--)q[-(g&1)-~g+(g|-2)-(J|0)]=V>>J*8&255;return q},Y=function(g,V,q,J,P,t,W){W=this;try{hU(q,g,this,J,V,P,t)}catch(y){T3(35,12,2048,y,this),P(function(L){L(W.A)})}},JQ=function(){return Vs.call(this,12)},MW=function(g,V,q,J,P,t){while(true)if(P.Qk.length>J?jO(q,g,[xn,36],P):(P.Qk.push(P.l.slice()),P.l[V]=void 0,B(V,t,P)),[])break},VS=function(g,V){function q(){(this.L=[],this).n=0}return[(g=(V=new (q.prototype.GW=(q.prototype.JN=function(J,P){return vL.call(this,J,12,P)},function(){return gN.call(this,56,73)}),q),new q),function(J){(V.JN(J),g).JN(J)}),function(J){return g=new (J=V.GW().concat(g.GW()),q),J}]},Rn=function(g,V,q){for(q=71;q!=82;)if(q==71)V=[],q=41;else if(q==41)q=90;else if(q==90)q=g--?49:68;else if(q==77)q=90;else if(q==49)V.push(Math.random()*255|0),q=77;else if(q==68)return V},fa=function(g,V,q,J,P,t,W,y){for(![]==false;!V.A;Number()){V.e8++;try{for(P=(y=(t=V.W,void 0),0);--J;)try{if(W=void 0,V.I)y=xB(V.I,V);else{if((P=Q(10,V),P)>=t)break;y=(W=r(23,(R(V,131,P),V)),O(W,V))}(y&&y[vu]&2048?y(V,J):jO(0,q,[xn,21,W],V),h)(false,false,14,26,J,V)}catch(L){Q(g,V)?jO(22,q,L,V):B(g,L,V)}switch(!!J){case []!=true:undefined;break;case true==[]:for(undefined;V.XL;![]==Number()){fa(124,V,(V.e8--,123),596386787031);return;if(true)break}jO(0,q,[xn,33],V);break}}catch(L){try{jO(22,q,L,V)}catch(n){T3(35,8,2048,n,V)}}if(V.e8--,[])break}},hR=function(){return LG.call(this,4,8)},cL=function(g,V,q,J,P,t){return Vs.call(this,34,g,V,q,J,P,t)},N$=function(g,V){return ys.call(this,71,32,g,V)},fG=q$(6,0,"object",10,"Math",this),M=(Vs(7,0,null,1,".","Symbol",function(g,V,q,J,P,t){for(t=93;t!=0;)if(t==65)t=g?94:59;else if(t==93)P=function(W,y){(this.O7=W,YB)(this,"description",{configurable:true,writable:true,value:y})},J=function(W,y){for(y=77;y!=68;){if(y==97)throw new TypeError("Symbol is not a constructor");if(y==77)y=this instanceof J?97:42;else if(y==42)return new P(V+(W||"")+"_"+q++,W)}},t=65;else{if(t==59)return P.prototype.toString=function(){return this.O7},V="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",q=0,J;if(t==94)return g}}),this||self),Z4="closure_uid_"+(Math.random()*1E9>>>0),na=0,uY,$B=function(g,V,q,J,P,t){for(t=(P=80,57);;)try{if(P==32)break;else if(P==80)P=M.addEventListener&&Object.defineProperty?92:1;else{if(P==1)return false;if(P==92)V=false,g=Object.defineProperty({},"passive",{get:function(){V=true}}),P=0;else if(P==94)t=57,P=64;else{if(P==64)return t=57,V;P==0&&(t=29,q=function(){},M.addEventListener("test",q,g),M.removeEventListener("test",q,g),P=64)}}}catch(W){if(t==57)throw W;t==29&&(J=W,P=94)}}();(EO.prototype.dispose=function(g){for(g=15;g!=83;)g==15?g=this.j?83:56:g==56&&(this.j=true,this.Z(),g=83)},EO.prototype).j=false,EO.prototype[Symbol.dispose]=function(){this.dispose()};while({})if((EO.prototype.Z=function(g){for(g=99;g!=51;)g==75?g=this.nn.length?21:51:g==26?g=75:g==21?(this.nn.shift()(),g=37):g==99?g=this.nn?26:51:g==37&&(g=75)},![]!=0)==![])break;sG.prototype.stopPropagation=(sG.prototype.preventDefault=function(){this.defaultPrevented=true},function(){this.hN=true}),OO(2,25,2,N$,sG),N$.prototype.init=function(g,V,q,J,P,t){{t=72;while(t!=8)t==32?(this.relatedTarget=q,t=35):t==47?(q=g.fromElement,t=32):t==71?(this.offsetX=g.offsetX,this.offsetY=g.offsetY,this.clientX=g.clientX!==void 0?g.clientX:g.pageX,this.clientY=g.clientY!==void 0?g.clientY:g.pageY,this.screenX=g.screenX||0,this.screenY=g.screenY||0,t=97):t==88?(this.clientX=J.clientX!==void 0?J.clientX:J.pageX,this.clientY=J.clientY!==void 0?J.clientY:J.pageY,this.screenX=J.screenX||0,this.screenY=J.screenY||0,t=97):t==93?t=P=="mouseover"?47:3:t==3?t=P=="mouseout"?74:32:t==35?t=J?88:71:t==97?(this.button=g.button,this.keyCode=g.keyCode||0,this.key=g.key||"",this.charCode=g.charCode||(P=="keypress"?g.keyCode:0),this.ctrlKey=g.ctrlKey,this.altKey=g.altKey,this.shiftKey=g.shiftKey,this.metaKey=g.metaKey,this.pointerId=g.pointerId||0,this.pointerType=g.pointerType,this.state=g.state,this.timeStamp=g.timeStamp,this.J=g,g.defaultPrevented&&N$.v.preventDefault.call(this),t=8):t==74?(q=g.toElement,t=32):t==39?t=q?32:93:t==72&&(P=this.type=g.type,J=g.changedTouches&&g.changedTouches.length?g.changedTouches[0]:null,this.target=g.target||g.srcElement,this.currentTarget=V,q=g.relatedTarget,t=39)}};while(!![])if(null!=(NaN===(N$.prototype.stopPropagation=function(){(N$.v.stopPropagation.call(this),this.J).stopPropagation?this.J.stopPropagation():this.J.cancelBubble=true},NaN)))break;var rN="closure_listenable_"+((N$.prototype.preventDefault=function(g){N$.v.preventDefault.call(this),g=this.J,g.preventDefault?g.preventDefault():g.returnValue=false},Math.random())*1E6|0),iK="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" "),AU=0;(sO.prototype.add=(sO.prototype.remove=function(g,V,q,J,P,t,W,y){for(y=6;y!=58;)if(y==91)y=W in this.h?82:65;else if(y==82)t=this.h[W],P=h(t,V,0,66,J,q),y=96;else{if(y==38)return false;if(y==50)return true;if(y==68)delete this.h[W],this.Cn--,y=50;else{if(y==65)return false;y==7?(k(9,null,t[P]),Array.prototype.splice.call(t,P,1),y=83):y==83?y=t.length==0?68:50:y==6?(W=g.toString(),y=91):y==96&&(y=P>-1?7:38)}}},function(g,V,q,J,P,t,W,y,L,n){for(n=44;n!=40;)if(n==61)t.zm=false,n=24;else if(n==73)n=L?3:43;else if(n==43)L=this.h[W]=[],this.Cn++,n=3;else if(n==78)t=new Qs(V,this.src,W,!!J,P),t.zm=q,L.push(t),n=24;else{if(n==24)return t;n==34?n=q?24:61:n==3?(y=h(L,V,0,64,P,J),n=49):n==49?n=y>-1?23:78:n==44?(W=g.toString(),L=this.h[W],n=73):n==23&&(t=L[y],n=34)}}),sO).prototype.Vk=function(g,V,q,J,P,t){return(t=(P=-1,this.h)[J.toString()],t)&&(P=h(t,V,0,65,g,q)),P>-1?t[P]:null};while(Number()==![undefined])if(sO.prototype.hasListener=function(g,V,q,J,P){return jB(78,(q=(P=(J=g!==void 0)?g.toString():"",V!==void 0),6),false,true,function(t,W,y){for(y=3;y!=99;){if(y==49)return false;if(y==58)++W,y=15;else if(y==3)W=0,y=28;else if(y==45)y=J&&t[W].type!=P||q&&t[W].capture!=V?58:6;else{if(y==6)return true;y==28?y=15:y==15&&(y=W<t.length?45:49)}}},this.h)},![]==0)break;var wN="closure_lm_"+(Math.random()*1E6|0),qW={},z3=0,BL="__closure_events_fn_"+(Math.random()*1E9>>>0);(((f=(OO(2,9,2,pG,EO),pG.prototype[rN]=true,pG.prototype),f).DC=function(g){this.Nf=g},f.addEventListener=function(g,V,q,J){h(0,g,false,7,V,q,this,J)},f).removeEventListener=function(g,V,q,J){T(3,0,null,g,V,J,q,this)},f.dispatchEvent=function(g,V,q,J,P,t,W,y,L,n,E,A){{A=6;while(A!=26)if(A==64)A=5;else if(A==78)q.push(t),A=87;else if(A==25)A=y=0;else if(A==5)A=t?78:41;else if(A==9)A=t?69:41;else if(A==87)t=t.Nf,A=5;else if(A==40)y++,A=71;else if(A==37)y=P.length-1,A=3;else if(A==14)A=!W.hN&&y>=0?67:1;else if(A==61)y--,A=14;else if(A==80)W=new sG(W,J),A=49;else if(A==75)A=W instanceof sG?44:48;else if(A==4)A=P?37:1;else if(A==97)A=typeof W==="string"?80:75;else{if(A==82)return L;A==67?(E=W.currentTarget=P[y],L=XD(9,0,true,E,V,W)&&L,A=61):A==49?(L=true,A=4):A==48?(n=W,W=new sG(V,J),kn(W,n),A=49):A==6?(t=this.Nf,A=9):A==41?(P=q,W=g,J=this.U7,V=W.type||W,A=97):A==0?A=71:A==21?(E=W.currentTarget=J,L=XD(10,0,true,E,V,W)&&L,W.hN||(L=XD(41,0,false,E,V,W)&&L),A=39):A==71?A=!W.hN&&y<P.length?58:82:A==69?(q=[],A=64):A==39?A=P?25:82:A==1?A=W.hN?39:21:A==58?(E=W.currentTarget=P[y],L=XD(42,0,false,E,V,W)&&L,A=40):A==3?A=14:A==44&&(W.target=W.target||J,A=49)}}},f.Z=function(){pG.v.Z.call(this),this.C&&mz(25,0,null,this.C),this.Nf=null},f.Vk=function(g,V,q,J){return this.C.Vk(g,V,q,String(J))},f).hasListener=function(g,V){return this.C.hasListener(g!==void 0?String(g):void 0,V)};var g8;(f=(OO((T((((((("ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON","INPUT"]),f=hR.prototype,f.i=function(g,V){return(V=this.Tm,typeof g)==="string"?V.getElementById(g):g},f).getElementsByTagName=function(g,V){return(V||this.Tm).getElementsByTagName(String(g))},f.createElement=function(g,V,q){return q=String((V=this.Tm,g)),V.contentType==="application/xhtml+xml"&&(q=q.toLowerCase()),V.createElement(q)},f).createTextNode=function(g){return this.Tm.createTextNode(String(g))},f).appendChild=function(g,V){g.appendChild(V)},f.append=function(g,V){mz(15,null,0,false,"string",arguments,g,g.nodeType==9?g:g.ownerDocument||g.document)},f).canHaveChildren=function(g,V){for(V=44;V!=9;)if(V==44)V=g.nodeType!=1?30:39;else{if(V==39){switch(g.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return false}return true}if(V==30)return false}},f).removeNode=Ca,f.contains=function(g,V,q,J){for(J=73;J!=55;)if(J==88)J=typeof g.compareDocumentPosition!="undefined"?68:0;else if(J==0)J=43;else{if(J==68)return g==V||!!(q=g.compareDocumentPosition(V),~(q&16)- -1-2*~q+2*(~q|16));if(J==12)return false;if(J==43)J=V&&g!=V?89:22;else{if(J==46)return g==V||g.contains(V);if(J==89)V=V.parentNode,J=69;else if(J==69)J=43;else if(J==73)J=g&&V?92:12;else{if(J==22)return V==g;J==92&&(J=g.contains&&V.nodeType==1?46:88)}}}},25),Hu),Hu.prototype.uN=0,Hu.prototype.Ng="",2),10,2,HL,pG),HL.prototype),f).nT=Hu.PV(),f.i=function(){return this.S};while({})if(![]!=(f.getParent=function(){return this.tN},false)!=null)break;f.removeChild=(f.Z=(f.DC=function(g,V){for(V=27;V!=4;){if(V==17)throw Error("Method not supported");V==27?V=this.tN&&this.tN!=g?17:94:V==94&&(HL.v.DC.call(this,g),V=4)}},f.an=function(){this.Kn=(nG(this,function(g){g.Kn&&g.an()},13),this.lS&&mz(26,0,null,this.lS),false)},function(g){for(g=87;g!=53;)g==83?(nG(this,function(V){V.dispose()},12),!this.iN&&this.S&&Ca(this.S),this.tN=this.ZC=this.Y1=this.S=null,HL.v.Z.call(this),g=53):g==87?(this.Kn&&this.an(),g=96):g==96?g=this.lS?45:83:g==45&&(this.lS.dispose(),delete this.lS,g=83)}),function(g,V,q,J,P,t,W,y,L,n,E,A,Z){{Z=17;while(Z!=40)if(Z==57)Z=this.ZC&&t?81:16;else{if(Z==23)throw Error("Child is not in parent component");if(Z==62)y=g,Z=3;else{if(Z==61)throw Error("Unable to set parent component");if(Z==80)Z=V?94:62;else if(Z==18)L=g.nT,E=g,W=L.Ng+":"+(L.uN++).toString(36),P=E.CT=W,Z=90;else if(Z==30)Z=typeof g==="string"?50:38;else if(Z==3)Z=y==null?61:36;else if(Z==15)q=this.ZC,t in q&&delete q[t],Dq(1,this.Y1,g,34),Z=80;else if(Z==94)g.an(),g.S&&Ca(g.S),Z=62;else if(Z==24)Z=t&&g?15:1;else{if(Z==39)return g;Z==50?(n=g,Z=99):Z==38?Z=(P=g.CT)?90:18:Z==77?(g=A,Z=24):Z==81?(J=this.ZC,A=(J!==null&&t in J?J[t]:void 0)||null,Z=77):Z==99?(t=n,Z=57):Z==17?Z=g?30:1:Z==36?(y.tN=null,HL.v.DC.call(y,null),Z=1):Z==16?(A=null,Z=77):Z==90?(n=P,Z=99):Z==1&&(Z=g?39:23)}}}}});var tQ,ER={button:"pressed",checkbox:"checked",menuitem:"selected",menuitemcheckbox:"checked",menuitemradio:"checked",radio:(T(28,d8),"checked"),tab:"selected",treeitem:"selected"},dN=(((T(26,(OO(2,(((f=d8.prototype,f).Rn=function(g,V,q,J,P,t,W,y){(y=(tQ||(tQ={1:"disabled",8:"selected",16:"checked",64:"expanded"}),t=tQ[V],g).getAttribute("role")||null)?(P=ER[y]||t,W=t=="checked"||t=="selected"?P:t):W=t,(J=W)&&EG(91,3,"hidden"," ",g,q,J)},f.iS=function(g){return g.i()},f).o=function(g,V,q,J,P,t,W){{W=46;while(W!=12)W==71?W=P?14:12:W==17?((t=this.tt[g])&&this.E5(t,V,q),this.Rn(P,g,q),W=12):W==46?(P=V.i(),W=71):W==48?(J=this.vE(),J.replace(/\\xa0|\\s/g," "),this.tt={1:J+"-disabled",2:J+"-hover",4:J+"-active",8:J+"-selected",16:J+"-checked",32:J+"-focused",64:J+"-open"},W=17):W==14&&(W=this.tt?17:48)}},f.vE=function(){return"goog-control"},f.Ln=function(g,V,q,J,P,t,W,y,L){for(y=(W=68,78);;)try{if(W==12)break;else W==49?W=h(g,32,0,45)?59:67:W==24?(J.tabIndex=-1,J.removeAttribute("tabIndex"),W=12):W==35?W=V?80:24:W==67?W=(t=q.hasAttribute("tabindex"))?61:56:W==2?W=!V&&h(g,32,0,40)?3:67:W==3?(y=70,q.blur(),W=41):W==59?(JR("string",8,g,4)&&g.setActive(false),JR("string",9,g,32)&&ys(71,68,1,false,32,g)&&g.o(false,32),W=67):W==80?(J.tabIndex=0,W=12):W==68?W=g.WV&32&&(q=g.iS())?2:12:W==72?(y=78,W=41):W==58?(J=q,W=35):W==61?(P=q.tabIndex,t=typeof P==="number"&&P>=0&&P<32768,W=56):W==56?W=t!=V?58:12:W==41&&(y=78,W=49)}catch(n){if(y==78)throw n;y==70&&(L=n,W=72)}},f.E5=function(g,V,q,J){(J=V.i?V.i():V)&&(q?OG:gC)(J,[g])},11),2,JQ,d8),JQ)),JQ).prototype.vE=function(){return"goog-button"},JQ).prototype.Rn=function(g,V,q){switch(V){case 8:case 16:EG(91,48,"hidden"," ",g,q,"pressed");break;default:case 64:case 1:JQ.v.Rn.call(this,g,V,q)}},{});f=(OO(2,26,2,RR,HL),RR.prototype),f.cE=true;while([])if(![void!![]]==(f.eP=255,0))break;f.getState=function(){return this.Gm},(f.WV=39,f.E5=(f.isVisible=function(){return this.cE},f.Z=function(g){{g=59;while(g!=61)g==59?(RR.v.Z.call(this),g=52):g==52?g=this.X6?38:64:g==64?(delete this.N,this.Y=null,g=61):g==38&&(this.X6.dispose(),delete this.X6,g=64)}},f.isActive=function(){return h(this,4,0,43)},function(g,V,q){{q=87;while(q!=15)q==87?q=g?13:50:q==50?q=V&&this.Y&&Dq(1,this.Y,V,35)?27:15:q==23?(this.Y?vL(1,68,this.Y,V)>=0||this.Y.push(V):this.Y=[V],this.N.E5(V,this,true),q=15):q==3?(this.N.E5(V,this,false),q=15):q==27?q=this.Y.length==0?43:3:q==13?q=V?23:15:q==43&&(this.Y=null,q=3)}}),f).Y=(f.iS=(f.an=(f.isEnabled=function(){return!h(this,1,0,44)},function(){((RR.v.an.call(this),this).X6&&this.X6.detach(),this).isVisible()&&this.isEnabled()&&this.N.Ln(this,false)}),f.Gm=0,function(){return this.N.iS(this)}),null),f.SP=(f.o=function(g,V,q,J,P,t,W){for(W=80;W!=35;)W==12?(this.setActive(false),ys(71,5,1,false,2,this)&&this.o(false,2),W=0):W==62?W=this.WV&V&&g!=h(this,V,0,42)?42:35:W==42?(this.N.o(V,this,g),this.Gm=g?this.Gm|V:(P=this.Gm,-~P-(P^~V)+2*(~P&~V)+(P|~~V)),W=35):W==90?W=J?0:12:W==32?(J=!g,t=this.getParent(),W=94):W==94?W=t&&typeof t.isEnabled=="function"&&!t.isEnabled()||!ys(71,21,1,!J,1,this)?35:90:W==80?W=q||V!=1?62:32:W==0&&(this.isVisible()&&this.N.Ln(this,J),this.o(!J,1,true),W=35)},f.setActive=function(g){ys(71,20,1,g,4,this)&&this.o(g,4)},0);while(typeof RR!=="function"){throw Error("Invalid component class "+RR);if(null!=false)break}if(typeof d8!=="function")throw Error("Invalid renderer class "+d8);var Wm=jB(78,3,RR),In={passive:!(jB(78,8,(OO(2,((T((OO(((dN[Wm]=d8,jB)(78,9,"goog-control",function(){return new RR(null)}),2),27,2,M$,JQ),27),M$),M$.prototype.o=function(g,V,q,J,P){for(P=33;P!=25;)P==40?(J.disabled=q,P=25):P==38?P=J&&g==1?40:25:P==33&&(M$.v.o.call(this,g,V,q),J=V.i(),P=38)},M$).prototype.Rn=function(){},M$.prototype.Ln=function(){},9),2,uK,RR),uK.prototype.Z=function(){delete (delete (uK.v.Z.call(this),this).lG,this).aU},"goog-button"),function(){return new uK(null)}),0),capture:true},G3=M.requestIdleCallback?function(g){requestIdleCallback(function(){g()},{timeout:4})}:M.setImmediate?function(g){setImmediate(g)}:function(g){setTimeout(g,0)},eB,on=String.fromCharCode(105,110,116,101,103,67,104,101,99,107,66,121,112,97,115,115),QC=(Y.prototype.Mg=void 0,Y.prototype.rJ=(Y.prototype.m9="toString",Y.prototype.XL=false,void 0),[]),w8=[],SB=[],vu=[],mX=[],oR=[],$n=[],zl=[],xn={},Wu=(((an,function(){})(Rn),qr,XN,function(){})(VS),FN,xn).constructor,bK=((((f=Y.prototype,f).bN=function(g,V,q,J,P,t){return bY.call(this,45,V,g,q,29,J,P,t)},f).Qf=0,f.Vf=function(g,V,q,J,P){return gN.call(this,56,9,g,V,q,J,P)},f).D7=function(g,V,q,J,P,t,W,y,L,n){return tR.call(this,52,g,V,q,J,P,t,W,y,L,n)},f.dJ=function(){return ys.call(this,71,3)},void 0);f=(Y.prototype.U="create",f.LT=function(){return Zq.call(this,41,48)},f.HV=(f.G=(window.performance||{}).now?function(){return this.WE+window.performance.now()}:function(){return+new Date},function(g,V,q,J,P,t){return K.call(this,5,g,V,q,J,P,t)}),Y).prototype,f.R=function(g,V){return bK=function(){return V==g?-79:-61},V=(g={},{}),function(q,J,P,t,W,y,L,n,E,A,Z,w,b,I,v,x,m,u,X,U,G,c,D,lY,IR,l,yC,H,WL,C,VC,PL,p,aR,AQ,z,AR,JU,Bu,sR,UG,a){for(AQ=(a=(AR=undefined,z=66,33),false);;)try{if(z==74)break;else if(z==81)Yn(124,this,10,q[1],q[2]),z=30;else if(z==80)L=0,G="",z=12;else if(z==98)a=23,fa(124,this,123,8001),z=30;else if(z==17)z=VC==vu?67:30;else if(z==50)p=G,Q(275,this).length=t.shift(),Q(123,this)[0]=t.shift(),O(322,this).length=t.shift(),Q(330,this).length=t.shift(),O(39,this).length=t.shift(),O(1,this).length=t.shift(),F(400,this).length=t.shift(),O(295,this).length=t.shift(),JU=p,AR=77,z=30;else if(z==12)z=47;else if(z==31)z=VC==w8?81:46;else if(z==66)n=V,V=g,z=7;else if(z==84)z=VC==$n?64:70;else if(z==67){if(C=O(45,this),I=typeof Symbol!="undefined"&&Symbol.iterator&&C[Symbol.iterator])l=I.call(C);else if(typeof C.length=="number")l={next:vL(0,59,C)};else throw Error(String(C)+" is not an iterable or ArrayLike");z=(A=(yC=l,yC.next()),33)}else if(z==33)z=38;else if(z==43)A=yC.next(),z=38;else if(z==75)this.gx=b,this.W=this.gx.length<<3,B(302,[0,0,0],this),z=98;else if(z==8)a=23,z=43;else if(z==70)z=VC==oR?35:18;else if(z==46)z=VC==zl?45:17;else if(z==79)Gl(this,39,N(2,W.length).concat(W),166),z=85;else if(z==6)AR!==undefined?(z=AR,AR=undefined):z=74;else if(z==30)a=33,V=n,z=6;else if(z==35)q[1].push(O(275,this).length,O(123,this)[0],F(322,this).length,O(330,this).length,F(39,this).length,F(1,this).length,F(400,this).length,O(295,this).length),R(this,449,q[2]),this.l[391]&&Yn(124,this,10,Q(391,this),8001),z=30;else if(z==4)C.length=0,z=30;else if(z==65)D=w.charCodeAt(lY),z=63;else if(z==64)H=q[1],z=68;else if(z==47)z=L<E.length?72:50;else if(z==55)t=q[2],IR=N(2,(O(39,this).length|0)+2),u=this.g,this.g=this,z=97;else if(z==38)z=A.done?4:3;else if(z==53)lY++,z=24;else if(z==52)W=W.slice(0,1E6),Gl(this,39,[],197),Gl(this,39,[],36),z=79;else if(z==97)a=49,y=Q(482,this),y.length>0&&Gl(this,39,N(2,y.length).concat(y),48),eO(249,N(1,this.H+1>>1),39,this,87),eO(249,N(1,this[SB].length),39,this),U=this.j8?Q(322,this):Q(295,this),U.length>0&&Gl(this,330,N(2,U.length).concat(U),64),J=F(330,this),J.length>4&&eO(249,N(2,J.length).concat(J),39,this,65),c=0,c+=(x=Q(444,this),4094-(x|2047)+(x&-2048)-(~x&2047)),c-=(F(39,this).length|0)+5,W=Q(1,this),W.length>4&&(c-=(W.length|0)+3),c>0&&eO(249,N(2,c).concat(Rn(c)),39,this,53),z=57;else if(z==72)aR=E[L][this.m9](16),aR.length==1&&(aR="0"+aR),G+=aR,z=34;else if(z==3)Z=A.value,z=44;else if(z==96)z=G?5:80;else if(z==45)JU=Yn(124,this,10,q[1],8001),AR=73,z=30;else if(z==34)L++,z=47;else if(z==86)a=23,E=Rn(2).concat(Q(39,this)),E[1]=(v=E[0],(v&60)+~(v&60)-(~v^60)),E[3]=(WL=E[1],m=IR[0],(m|0)-(WL&m)-1-(~WL|m)),E[4]=(X=E[1],PL=IR[1],(PL|0)+~PL-(~X^PL)),G=this.E7(E),z=96;else if(z==68)a=38,w=atob(H),b=[],z=lY=P=0;else{if(z==77)return JU;if(z==44)a=41,Z(),z=43;else if(z==0)z=24;else if(z==24)z=lY<w.length?65:75;else if(z==7)a=23,VC=q[0],z=84;else if(z==78)a=23,jO(17,123,UG,this),AR=74,z=30;else if(z==29)b[P++]=D,z=53;else if(z==26)b[P++]=510-(D|255)+(D|-256)-(~D|255),D>>=8,z=29;else if(z==57)z=W.length>4?22:85;else if(z==5)G="*"+G,z=50;else if(z==63)z=D>255?26:29;else if(z==85)a=23,this.g=u,z=20;else if(z==18)z=VC==SB?55:31;else if(z==22)z=W.length>1E6?52:79;else{if(z==73)return JU;if(z==20)z=AR!==undefined?30:86;else if(z==2)throw Bu;}}}catch(tU){if(Bu=tU,a==33)throw tU;a==49?(AR=2,z=85):a==41?(sR=tU,z=8):a==23?(AR=2,z=30):a==38&&(UG=tU,z=78)}}}();while({})if((f.E7=function(g,V,q,J,P){return CG.call(this,48,"_",g,V,8,q,J,P)},![])==0)break;var SO=(((f.KT=0,f).Jt=0,f).Mf=function(){return tR.call(this,18)},f.TW=function(){return EG.call(this,91,8)},/./),Tl,Pm=$n.pop.bind((Y.prototype[mX]=[0,0,1,1,0,1,1],Y.prototype)[oR]);if((Tl=(SO[Y.prototype.m9]=Pm,nG(Y.prototype.U,{get:Pm},39)),![])!=true)Y.prototype.pT=void 0;var Ka=function(g,V){return(V=h("ks","error",null,20))&&g.eval(V.createScript("1"))===1?function(q){return V.createScript(q)}:function(q){return""+q}}(M);(eB=M.knitsail||(M.knitsail={}),eB.m)>40||(eB.m=41,eB.ks=NW,eB.a=FD),eB.fbX_=function(g,V,q,J,P,t,W,y){return[function(L){return Dq(false,L,y,9)},(y=new Y(t,J,W,g,V,P),function(L){y.Mf(L)})]};}).call(this);'].join('\n')));}).call(this);</script><script nonce="GNUl1Yy1fURJHDKhr2nUrw">(function(){var r='1';var ce=30;var sctm=false;var p='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\x3d';var g='knitsail';var eid='xFJ6aJ-vINzvkPIP_fiouQc';var ss_cgi=false;var sp='';var hashed_query='';var cbs='';var ussv='';(function(){var q=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},u=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,d){if(a==Array.prototype||a==Object.prototype)return a;a[b]=d.value;return a},w=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var d=a[b];if(d&&d.Math==Math)return d}throw Error("a");},x=w(this),y=function(a,b){if(b)a:{var d=x;a=a.split(".");for(var l=0;l<a.length-1;l++){var f=a[l];if(!(f in d))break a;d=d[f]}a=a[a.length-1];l=d[a];b=b(l);b!=l&&b!=null&&u(d,a,{configurable:!0,writable:!0,value:b})}},z=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:q(a)};throw Error("b`"+String(a));};y("Promise",function(a){function b(){this.i=null}function d(c){return c instanceof f?c:new f(function(e){e(c)})}if(a)return a;b.prototype.j=function(c){if(this.i==null){this.i=[];var e=this;this.l(function(){e.v()})}this.i.push(c)};var l=x.setTimeout;b.prototype.l=function(c){l(c,0)};b.prototype.v=function(){for(;this.i&&this.i.length;){var c=this.i;this.i=[];for(var e=0;e<c.length;++e){var h=c[e];c[e]=null;try{h()}catch(k){this.A(k)}}}this.i=null};b.prototype.A=function(c){this.l(function(){throw c;})};var f=function(c){this.j=0;this.l=void 0;this.i=[];this.D=!1;var e=this.A();try{c(e.resolve,e.reject)}catch(h){e.reject(h)}};f.prototype.A=function(){function c(k){return function(m){h||(h=!0,k.call(e,m))}}var e=this,h=!1;return{resolve:c(this.J),reject:c(this.v)}};f.prototype.J=function(c){if(c===this)this.v(new TypeError("A Promise cannot resolve to itself"));else if(c instanceof f)this.L(c);else{a:switch(typeof c){case "object":var e=c!=null;break a;case "function":e=!0;break a;default:e=!1}e?this.I(c):this.C(c)}};f.prototype.I=function(c){var e=void 0;try{e=c.then}catch(h){this.v(h);return}typeof e=="function"?this.M(e,c):this.C(c)};f.prototype.v=function(c){this.F(2,c)};f.prototype.C=function(c){this.F(1,c)};f.prototype.F=function(c,e){if(this.j!=0)throw Error("c`"+c+"`"+e+"`"+this.j);this.j=c;this.l=e;this.j===2&&this.K();this.G()};f.prototype.K=function(){var c=this;l(function(){if(c.H()){var e=x.console;typeof e!=="undefined"&&e.error(c.l)}},1)};f.prototype.H=function(){if(this.D)return!1;var c=x.CustomEvent,e=x.Event,h=x.dispatchEvent;if(typeof h==="undefined")return!0;typeof c==="function"?c=new c("unhandledrejection",{cancelable:!0}):typeof e==="function"?c=new e("unhandledrejection",{cancelable:!0}):(c=x.document.createEvent("CustomEvent"),c.initCustomEvent("unhandledrejection",!1,!0,c));c.promise=this;c.reason=this.l;return h(c)};f.prototype.G=function(){if(this.i!=null){for(var c=0;c<this.i.length;++c)v.j(this.i[c]);this.i=null}};var v=new b;f.prototype.L=function(c){var e=this.A();c.B(e.resolve,e.reject)};f.prototype.M=function(c,e){var h=this.A();try{c.call(e,h.resolve,h.reject)}catch(k){h.reject(k)}};f.prototype.then=function(c,e){function h(n,t){return typeof n=="function"?function(B){try{k(n(B))}catch(C){m(C)}}:t}var k,m,D=new f(function(n,t){k=n;m=t});this.B(h(c,k),h(e,m));return D};f.prototype.catch=function(c){return this.then(void 0,c)};f.prototype.B=function(c,e){function h(){switch(k.j){case 1:c(k.l);break;case 2:e(k.l);break;default:throw Error("d`"+k.j);}}var k=
this;this.i==null?v.j(h):this.i.push(h);this.D=!0};f.resolve=d;f.reject=function(c){return new f(function(e,h){h(c)})};f.race=function(c){return new f(function(e,h){for(var k=z(c),m=k.next();!m.done;m=k.next())d(m.value).B(e,h)})};f.all=function(c){var e=z(c),h=e.next();return h.done?d([]):new f(function(k,m){function D(B){return function(C){n[B]=C;t--;t==0&&k(n)}}var n=[],t=0;do n.push(void 0),t++,d(h.value).B(D(n.length-1),m),h=e.next();while(!h.done)})};return f});
var A=this||self;function E(){return window.performance&&window.performance.navigation&&window.performance.navigation.type};var F=window.location;function G(a){return(a=F.search.match(new RegExp("[?&]"+a+"=(\\d+)")))?Number(a[1]):-1}function H(){var a=google.timers.load;google.c.gts?google.c.gts(function(){I(a)}):I(a)}
function I(a){var b=a.e,d=google.stvsc;d&&(b.ssr=1);if(d?d.isBF:E()===2)b.bb=1;E()===1&&(b.r=1);a:{if(window.performance&&window.performance.getEntriesByType&&(d=window.performance.getEntriesByType("navigation"),d.length!==0)){d=d[0];break a}d=void 0}if(d){var l=d.type;l&&(b.nt=l);l=d.deliveryType;l!=null&&(b.dt=l);d=d.transferSize;d!=null&&(b.ts=d)}(d=window.navigation)&&(d=d.activation)&&(d=d.navigationType)&&(b.ant=d);b=a.m;if(!b||!b.prs){d=window._csc==="agsa"&&window._cshid;l=E()||d?0:G("qsubts");l>0&&(b=G("fbts"),b>0&&(a.t.start=Math.max(l,b)));var f=a.t,v=f.start;b={};a.wsrt!==void 0&&(b.wsrt=a.wsrt);if(v)for(var c in f)if(c!=="start"){var e=f[c];b[c]=c==="sgl"?e:Math.max(e-v,0)}l>0&&(b.gsasrt=a.t.start-l,c=G("qsd"),c>0&&google.c.e("load","qsd",String(c)),(c=a.fbts)&&(b.gsasrt2=Math.max(l,c)-l));E()||d||!a.qsubts||(c=a.fbts)&&(b.gsasrt3=Math.max(a.qsubts,c)-a.qsubts);c=a.e;a="/gen_204?s="+google.sn+"&t=sg&atyp=csi&ei="+google.kEI+"&rt=";d="";for(k in b)a+=""+d+k+"."+b[k],d=",";for(var h in c)a+=
"&"+h+"="+c[h];var k="";A._cshid&&(k+="&cshid="+A._cshid);(h=window.google&&window.google.kOPI||null)&&(k+="&opi="+h);k=a+=k;typeof navigator.sendBeacon==="function"?navigator.sendBeacon(k,""):google.log("","",k)}};var J=function(){var a=location.href;this.i=this.j="";var b=a.indexOf("#");b>0&&(this.j=a.substring(b),a=a.substring(0,b));b=a.indexOf("?");b>0&&(this.i="&"+a.substring(b+1),a=a.substring(0,b));this.l=a},L=function(a,b,d){K(a,b);a.i=a.i+"&"+b+"="+d},K=function(a,b){a.i=a.i.replace(new RegExp("&"+b+"=([^&]+)","g"),"")};J.prototype.toString=function(){return""+this.l+(this.i?"?"+this.i.substring(1):"")+this.j};
var M=function(a){this.i=a};M.prototype.toString=function(){return this.i};var N=function(a){this.N=a};function O(a){return new N(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var P=[O("data"),O("http"),O("https"),O("mailto"),O("ftp"),new N(function(a){return/^[^:]*([/?#]|$)/.test(a)})],Q=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;function R(){var a=A[g];if(a){a=z((0,a.a)(p,function(){},!1)).next().value;var b=[aa()];return a(b)}S(Error("f"))}function aa(){var a=location.href,b=hashed_query,d={};b&&(d.qh=b,(a=(a=a.match(/[?&]start=(\d+)/g))?a[a.length-1].match(/\d+/)[0]:"")&&(d.st=a));return d}
function T(){var a;a:{if(window.st&&(a=window.st(location.href)))break a;a=performance&&performance.timing&&performance.timing.navigationStart?performance.timing.navigationStart:void 0}if(a)try{var b;((b=window)==null?0:b.sessionStorage)&&window.sessionStorage.setItem(eid,String(a))}catch(d){}}function U(){var a=eid,b=new J;K(b,"sg_ss");L(b,"sei",a);return b.toString()}function V(a){var b=eid,d=new J;L(d,"sg_ss",encodeURIComponent(a));L(d,"sei",b);W(d.toString())}
function ba(a){if(window.prs){X("psrt");sctm&&H();var b=U();window.prs(b,a).catch(function(){V(a)})}else V(a)}function W(a){X("psrt");sctm&&H();window.prs?window.prs(a).catch(function(){Y(a)}):Y(a)}
function Y(a){if(window.pr)window.pr(a);else{a:{var b=b===void 0?P:b;if(a instanceof M)b=a;else{for(var d=0;d<b.length;++d){var l=b[d];if(l instanceof N&&l.N(a)){b=new M(a);break a}}b=void 0}}a=location;if(b instanceof M)if(b instanceof M)b=b.i;else throw Error("e");else b=Q.test(b)?b:void 0;b!==void 0&&a.replace(b)}}function S(a){navigator.sendBeacon("/gen_204?cad=sg_b_e&e="+a,"")}function X(a){sctm&&google.tick("load",a)};navigator||(A.navigator={});typeof navigator.sendBeacon!=="function"&&(navigator.sendBeacon=function(a){(new Image).src=a});window.onerror=function(a,b,d,l,f){navigator.sendBeacon("/gen_204?emsg="+(f instanceof Error?f.message:a)+"&srcpg=sgs&jsr=1&jsel=3")};X("sst");var Z;window.sgs&&ussv?(X("ssst"),Z=window.sgs(sp).then(function(a){X("sset");r&&(T(),ba(a));return!0},function(){return!1})):Z=Promise.resolve(!1);Z.then(function(a){if(!a&&(X("bsst"),a=R(),X("bset"),a)){var b=cbs;a=hashed_query?"B.1."+b+"."+a:a;b=new Date;b.setSeconds(b.getSeconds()+(Number(ce)||300));var d="SG_SS="+a,l=document.cookie.length+d.length;r&&(l<4093&&!ss_cgi&&(document.cookie=d+("; expires="+b.toUTCString())),T(),ss_cgi||document.cookie.indexOf("SG_SS=")<0?V(a):W(U()))}}).catch(function(a){S(a)});}).call(this);})();</script><script nonce="GNUl1Yy1fURJHDKhr2nUrw">(function(){var cssId='yvlrue';var event_id='xFJ6aJ-vINzvkPIP_fiouQc';function sw(){document.getElementById(cssId).setAttribute('style','');navigator.sendBeacon(`/gen_204?cad=sg_trbl&ei=${event_id}`,'');}
setTimeout(sw,2000);})();</script><style>div{font-family:sans-serif;color:#545454;background-color:#fff}a{color:#1558d6;font-size:inherit;text-decoration:none}a:visited{color:#681da8}</style><div id="yvlrue" style="display:none">If you're having trouble accessing Google Search, please&nbsp;<a href="/search?q=python+programming&amp;num=5&amp;sca_esv=b3f6a4e4ace9f962&amp;hl=en&amp;ie=UTF-8&amp;emsg=SG_REL&amp;sei=xFJ6aJ-vINzvkPIP_fiouQc">click here</a>, or send&nbsp;<a href="https://support.google.com/websearch">feedback</a>.</div></body></html>