# CogBridges Search - Reddit搜索分析系统

## 项目简介

CogBridges Search是一个基于HTTP请求和Reddit API的智能搜索分析系统，提供极简的搜索界面和深度的内容分析功能。采用MVP方式实现，无需复杂的Google API配置。

## 功能特性

### 核心功能
- 🔍 **极简搜索界面** - 类似Google搜索首页的简洁设计
- 🌐 **Google搜索集成** - 使用HTTP请求解析Google搜索结果，无需API密钥
- 📊 **Reddit深度分析** - 获取帖子内容、热门评论及用户历史数据
- 🚀 **并行数据获取** - 高效的异步API调用，提升性能
- 📝 **完整日志记录** - JSON格式保存所有数据，便于后续分析

### 技术特性
- 🔧 **代理支持** - 内置Clash代理配置，确保API访问稳定
- 🎯 **参数可配置** - 灵活的搜索结果数量和分析深度配置
- 🧪 **完整测试覆盖** - 单元测试和集成测试确保代码质量
- 🎨 **Silicon Valley风格UI** - 现代化的前端设计

## 业务流程

1. **搜索阶段**
   - 用户在极简搜索框中输入查询
   - 使用HTTP请求搜索Google，解析HTML结果
   - 过滤Reddit内容（site:reddit.com），获取前5个结果URL

2. **内容分析阶段**
   - 使用Reddit API获取帖子详细内容
   - 并行获取每个帖子的top 6一级评论
   - 提取评论者信息

3. **用户画像阶段**
   - 并行获取所有评论者的历史数据
   - 获取每个用户在相关子版块的top 20评论
   - 获取每个用户在相关子版块的top 10帖子

4. **数据存储阶段**
   - 所有数据以JSON格式保存
   - 完整的操作日志记录
   - 支持数据回溯和分析

## 项目结构

```
cogbridges_search/
├── README.md                 # 项目说明
├── requirements.txt          # Python依赖
├── config.py                # 配置管理
├── app.py                   # Flask应用主文件
├── services/                # 服务层
│   ├── __init__.py
│   ├── google_search.py     # Google搜索服务
│   ├── reddit_service.py    # Reddit API服务
│   └── data_service.py      # 数据存储服务
├── models/                  # 数据模型
│   ├── __init__.py
│   ├── search_models.py     # 搜索相关模型
│   └── reddit_models.py     # Reddit数据模型
├── utils/                   # 工具函数
│   ├── __init__.py
│   ├── proxy_utils.py       # 代理配置工具
│   └── logger_utils.py      # 日志工具
├── static/                  # 静态文件
│   ├── css/
│   │   └── style.css        # 样式文件
│   └── js/
│       └── app.js           # 前端JavaScript
├── templates/               # HTML模板
│   └── index.html           # 主页模板
├── data/                    # 数据存储目录
│   ├── logs/                # 日志文件
│   └── results/             # 搜索结果
├── tests/                   # 测试文件
│   ├── __init__.py
│   ├── test_google_search.py
│   ├── test_reddit_service.py
│   ├── test_data_service.py
│   └── test_integration.py
└── .env.example             # 环境变量示例
```

## 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件，填入Reddit API配置
# - REDDIT_CLIENT_ID
# - REDDIT_CLIENT_SECRET
# - REDDIT_USER_AGENT
```

### 3. 启动应用

```bash
python app.py
```

访问 http://localhost:5000 开始使用。

### 4. 运行测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_integration.py -v
```

## API配置说明

### Google Custom Search API
- 需要在Google Cloud Console创建项目
- 启用Custom Search API
- 创建自定义搜索引擎，配置搜索Reddit

### Reddit API
- 在Reddit创建应用获取client_id和client_secret
- 配置适当的用户代理字符串

### 代理配置
- 支持HTTP/HTTPS代理
- 默认使用Clash代理配置（127.0.0.1:7890）

## 开发指南

### 代码规范
- 遵循PEP 8 Python代码规范
- 使用类型提示提高代码可读性
- 完整的文档字符串和注释

### 测试要求
- 所有核心功能必须有单元测试
- 集成测试覆盖完整业务流程
- 测试覆盖率要求 > 80%

### 日志规范
- 使用结构化日志记录
- 不同级别的日志分类存储
- 敏感信息脱敏处理

## 许可证

MIT License - 详见 LICENSE 文件

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

---

**注意**: 使用本项目需要遵守Google和Reddit的API使用条款，请合理使用API配额。
