#!/usr/bin/env python3
"""
CogBridges - 完整集成测试
测试从Google搜索到Reddit数据分析的完整端到端业务流程
使用模拟前端输入："Should I subscribe to <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, or <PERSON>?"
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path
import sys

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services.cogbridges_service import CogBridgesService
from utils.logger_utils import get_logger


class IntegrationTestRunner:
    """完整集成测试运行器"""
    
    def __init__(self):
        """初始化测试运行器"""
        self.logger = get_logger(__name__)
        self.test_query = "Should I subscribe to GPT, Claude, Grok, or Gemini?"
        self.results_dir = Path("test_results")
        self.results_dir.mkdir(exist_ok=True)
        
    async def run_complete_integration_test(self):
        """运行完整的集成测试"""
        print("🚀 CogBridges 完整集成测试")
        print("=" * 60)
        print(f"📝 测试查询: {self.test_query}")
        print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("-" * 60)
        
        start_time = time.time()
        
        try:
            # 初始化CogBridges服务
            print("🔧 初始化CogBridges服务...")
            cogbridges = CogBridgesService()
            print("✅ 服务初始化成功")
            
            # 执行完整的业务流程
            print(f"\n🔍 开始执行完整业务流程...")
            result = await cogbridges.search(self.test_query)
            
            # 验证结果
            success = await self._validate_results(result)
            
            # 保存结果
            await self._save_test_results(result, success)
            
            # 显示测试总结
            self._display_test_summary(result, success, time.time() - start_time)
            
            return success
            
        except Exception as e:
            self.logger.error(f"集成测试失败: {e}")
            print(f"❌ 集成测试失败: {e}")
            return False
    
    async def _validate_results(self, result):
        """验证测试结果"""
        print("\n🔍 验证测试结果...")
        
        validation_results = {
            "overall_success": result.success,
            "google_search": False,
            "reddit_posts": False,
            "commenters_history": False,
            "data_completeness": False,
            "performance": False
        }
        
        # 验证Google搜索结果
        if result.google_results and len(result.google_results) > 0:
            validation_results["google_search"] = True
            print(f"✅ Google搜索: 找到 {len(result.google_results)} 个结果")
        else:
            print("❌ Google搜索: 未找到结果")
        
        # 验证Reddit帖子数据
        if result.reddit_posts and len(result.reddit_posts) > 0:
            validation_results["reddit_posts"] = True
            print(f"✅ Reddit帖子: 获取 {len(result.reddit_posts)} 个帖子")
        else:
            print("❌ Reddit帖子: 未获取到数据")
        
        # 验证评论者历史数据
        if result.commenters_history and len(result.commenters_history) > 0:
            validation_results["commenters_history"] = True
            print(f"✅ 评论者历史: 分析 {len(result.commenters_history)} 个用户")
        else:
            print("❌ 评论者历史: 未获取到数据")
        
        # 验证数据完整性
        if (validation_results["google_search"] and 
            validation_results["reddit_posts"] and 
            validation_results["commenters_history"]):
            validation_results["data_completeness"] = True
            print("✅ 数据完整性: 所有步骤数据完整")
        else:
            print("❌ 数据完整性: 部分步骤数据缺失")
        
        # 验证性能指标
        if result.total_time < 120:  # 2分钟内完成
            validation_results["performance"] = True
            print(f"✅ 性能指标: 总耗时 {result.total_time:.2f}秒 (< 120秒)")
        else:
            print(f"⚠️ 性能指标: 总耗时 {result.total_time:.2f}秒 (> 120秒)")
        
        # 计算总体成功率
        success_count = sum(1 for v in validation_results.values() if v)
        total_checks = len(validation_results)
        success_rate = success_count / total_checks
        
        print(f"\n📊 验证结果: {success_count}/{total_checks} 项通过 ({success_rate:.1%})")
        
        return success_rate >= 0.8  # 80%以上通过率视为成功
    
    async def _save_test_results(self, result, success):
        """保存测试结果到JSON文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"integration_test_results_{timestamp}.json"
        filepath = self.results_dir / filename
        
        # 构建测试结果数据
        test_results = {
            "test_info": {
                "test_name": "CogBridges完整集成测试",
                "test_query": self.test_query,
                "timestamp": datetime.now().isoformat(),
                "success": success
            },
            "performance_metrics": {
                "total_time": result.total_time,
                "google_search_time": result.google_search_time,
                "reddit_posts_time": result.reddit_posts_time,
                "commenters_history_time": result.commenters_history_time
            },
            "data_statistics": {
                "google_results_count": len(result.google_results) if result.google_results else 0,
                "reddit_posts_count": len(result.reddit_posts) if result.reddit_posts else 0,
                "commenters_count": len(result.commenters_history) if result.commenters_history else 0
            },
            "business_flow_results": {
                "step1_google_search": {
                    "success": bool(result.google_results),
                    "results": result.google_results[:3] if result.google_results else [],
                    "time_taken": result.google_search_time
                },
                "step2_reddit_posts": {
                    "success": bool(result.reddit_posts),
                    "posts_summary": [
                        {
                            "title": post.get("title", "")[:100],
                            "subreddit": post.get("subreddit", ""),
                            "comments_count": len(post.get("comments", []))
                        }
                        for post in (result.reddit_posts[:3] if result.reddit_posts else [])
                    ],
                    "time_taken": result.reddit_posts_time
                },
                "step3_commenters_history": {
                    "success": bool(result.commenters_history),
                    "users_analyzed": list(result.commenters_history.keys())[:5] if result.commenters_history else [],
                    "time_taken": result.commenters_history_time
                }
            },
            "error_info": {
                "has_error": not result.success,
                "error_message": result.error_message if hasattr(result, 'error_message') else ""
            }
        }
        
        # 保存到文件
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(test_results, f, ensure_ascii=False, indent=2)
            
            print(f"💾 测试结果已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"保存测试结果失败: {e}")
            print(f"❌ 保存测试结果失败: {e}")
            return None
    
    def _display_test_summary(self, result, success, total_time):
        """显示测试总结"""
        print("\n" + "=" * 60)
        print("📋 测试总结")
        print("=" * 60)
        
        # 总体结果
        status_icon = "✅" if success else "❌"
        status_text = "成功" if success else "失败"
        print(f"{status_icon} 总体结果: {status_text}")
        print(f"⏱️ 总耗时: {total_time:.2f}秒")
        
        # 各步骤详情
        print(f"\n📊 业务流程详情:")
        print(f"  🔍 步骤1 - Google搜索: {result.google_search_time:.2f}秒, {len(result.google_results) if result.google_results else 0}个结果")
        print(f"  📝 步骤2 - Reddit帖子: {result.reddit_posts_time:.2f}秒, {len(result.reddit_posts) if result.reddit_posts else 0}个帖子")
        print(f"  👥 步骤3 - 评论者历史: {result.commenters_history_time:.2f}秒, {len(result.commenters_history) if result.commenters_history else 0}个用户")
        
        # 性能指标
        print(f"\n⚡ 性能指标:")
        print(f"  平均每步耗时: {(result.google_search_time + result.reddit_posts_time + result.commenters_history_time) / 3:.2f}秒")
        print(f"  数据获取效率: {(len(result.google_results or []) + len(result.reddit_posts or []) + len(result.commenters_history or {})) / max(total_time, 1):.1f} 项/秒")
        
        # 建议
        if success:
            print(f"\n🎉 集成测试通过！系统运行正常。")
        else:
            print(f"\n⚠️ 集成测试未完全通过，建议检查:")
            if not result.google_results:
                print("  - Google Custom Search API配置")
            if not result.reddit_posts:
                print("  - Reddit API连接和数据获取")
            if not result.commenters_history:
                print("  - 用户历史数据分析逻辑")


async def main():
    """主函数"""
    runner = IntegrationTestRunner()
    success = await runner.run_complete_integration_test()
    
    if success:
        print(f"\n🎊 集成测试完成！系统已准备就绪。")
        sys.exit(0)
    else:
        print(f"\n💥 集成测试失败，请检查系统配置。")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
